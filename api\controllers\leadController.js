const { query, transaction } = require('../config/database');
const Joi = require('joi');

// Validation schemas
const createLeadSchema = Joi.object({
    name: Joi.string().min(2).max(200).required(),
    phone: Joi.string().pattern(/^\+?[1-9]\d{1,14}$/).required(),
    course_preferences: Joi.array().items(Joi.string()).default([]),
    source: Joi.string().default('landing_page'),
    notes: Joi.string().allow('').optional()
});

const updateLeadSchema = Joi.object({
    name: Joi.string().min(2).max(200).optional(),
    phone: Joi.string().pattern(/^\+?[1-9]\d{1,14}$/).optional(),
    course_preferences: Joi.array().items(Joi.string()).optional(),
    status: Joi.string().valid('new', 'contacted', 'interested', 'not_interested', 'callback_scheduled', 'test_scheduled', 'enrolled', 'lost').optional(),
    assigned_agent_id: Joi.string().uuid().allow(null).optional(),
    notes: Joi.string().allow('').optional(),
    follow_up_date: Joi.date().allow(null).optional()
});

// Create new lead
const createLead = async (leadData) => {
    try {
        // Validate input data
        const { error, value } = createLeadSchema.validate(leadData);
        if (error) {
            return {
                success: false,
                message: error.details[0].message
            };
        }

        const { name, phone, course_preferences, source, notes } = value;

        // Check if lead with same phone already exists
        const existingLead = await query(
            'SELECT id, status FROM leads WHERE phone = $1 AND status NOT IN ($2, $3)',
            [phone, 'enrolled', 'lost']
        );

        if (existingLead.rows.length > 0) {
            // Update existing lead instead of creating new one
            const leadId = existingLead.rows[0].id;
            await query(
                `UPDATE leads SET 
                 name = $1, 
                 course_preferences = $2, 
                 source = $3, 
                 notes = COALESCE(notes, '') || CASE WHEN notes IS NOT NULL AND notes != '' THEN E'\n' ELSE '' END || $4,
                 updated_at = CURRENT_TIMESTAMP
                 WHERE id = $5`,
                [name, course_preferences, source, notes || `Updated from ${source}`, leadId]
            );

            return {
                success: true,
                message: 'Lead updated successfully',
                data: { id: leadId, isUpdate: true }
            };
        }

        // Create new lead
        const result = await query(
            `INSERT INTO leads (name, phone, course_preferences, source, notes)
             VALUES ($1, $2, $3, $4, $5)
             RETURNING id, name, phone, course_preferences, status, source, created_at`,
            [name, phone, course_preferences, source, notes]
        );

        return {
            success: true,
            message: 'Lead created successfully',
            data: result.rows[0]
        };
    } catch (error) {
        console.error('Error creating lead:', error);
        return {
            success: false,
            message: 'Failed to create lead'
        };
    }
};

// Get all leads with filtering and pagination
const getLeads = async (filters = {}, pagination = {}) => {
    try {
        const { 
            status, 
            assigned_agent_id, 
            source, 
            search,
            date_from,
            date_to 
        } = filters;
        
        const { 
            page = 1, 
            limit = 20, 
            sort_by = 'created_at', 
            sort_order = 'DESC' 
        } = pagination;

        let whereConditions = [];
        let queryParams = [];
        let paramIndex = 1;

        // Build WHERE conditions
        if (status) {
            whereConditions.push(`l.status = $${paramIndex++}`);
            queryParams.push(status);
        }

        if (assigned_agent_id) {
            whereConditions.push(`l.assigned_agent_id = $${paramIndex++}`);
            queryParams.push(assigned_agent_id);
        }

        if (source) {
            whereConditions.push(`l.source = $${paramIndex++}`);
            queryParams.push(source);
        }

        if (search) {
            whereConditions.push(`(l.name ILIKE $${paramIndex} OR l.phone ILIKE $${paramIndex})`);
            queryParams.push(`%${search}%`);
            paramIndex++;
        }

        if (date_from) {
            whereConditions.push(`l.created_at >= $${paramIndex++}`);
            queryParams.push(date_from);
        }

        if (date_to) {
            whereConditions.push(`l.created_at <= $${paramIndex++}`);
            queryParams.push(date_to);
        }

        const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

        // Count total records
        const countQuery = `
            SELECT COUNT(*) as total
            FROM leads l
            ${whereClause}
        `;
        const countResult = await query(countQuery, queryParams);
        const total = parseInt(countResult.rows[0].total);

        // Calculate pagination
        const offset = (page - 1) * limit;
        const totalPages = Math.ceil(total / limit);

        // Get leads with agent information
        const leadsQuery = `
            SELECT 
                l.id,
                l.name,
                l.phone,
                l.course_preferences,
                l.status,
                l.source,
                l.notes,
                l.follow_up_date,
                l.created_at,
                l.updated_at,
                u.first_name as agent_first_name,
                u.last_name as agent_last_name
            FROM leads l
            LEFT JOIN users u ON l.assigned_agent_id = u.id
            ${whereClause}
            ORDER BY l.${sort_by} ${sort_order}
            LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
        `;
        
        queryParams.push(limit, offset);
        const leadsResult = await query(leadsQuery, queryParams);

        return {
            success: true,
            data: {
                leads: leadsResult.rows,
                pagination: {
                    current_page: page,
                    total_pages: totalPages,
                    total_records: total,
                    records_per_page: limit,
                    has_next: page < totalPages,
                    has_prev: page > 1
                }
            }
        };
    } catch (error) {
        console.error('Error getting leads:', error);
        return {
            success: false,
            message: 'Failed to retrieve leads'
        };
    }
};

// Get lead by ID
const getLeadById = async (leadId) => {
    try {
        const result = await query(
            `SELECT 
                l.*,
                u.first_name as agent_first_name,
                u.last_name as agent_last_name,
                u.phone as agent_phone
            FROM leads l
            LEFT JOIN users u ON l.assigned_agent_id = u.id
            WHERE l.id = $1`,
            [leadId]
        );

        if (result.rows.length === 0) {
            return {
                success: false,
                message: 'Lead not found'
            };
        }

        return {
            success: true,
            data: result.rows[0]
        };
    } catch (error) {
        console.error('Error getting lead by ID:', error);
        return {
            success: false,
            message: 'Failed to retrieve lead'
        };
    }
};

// Update lead
const updateLead = async (leadId, updateData) => {
    try {
        // Validate input data
        const { error, value } = updateLeadSchema.validate(updateData);
        if (error) {
            return {
                success: false,
                message: error.details[0].message
            };
        }

        // Build dynamic update query
        const updateFields = [];
        const queryParams = [];
        let paramIndex = 1;

        Object.keys(value).forEach(key => {
            if (value[key] !== undefined) {
                updateFields.push(`${key} = $${paramIndex++}`);
                queryParams.push(value[key]);
            }
        });

        if (updateFields.length === 0) {
            return {
                success: false,
                message: 'No fields to update'
            };
        }

        updateFields.push(`updated_at = CURRENT_TIMESTAMP`);
        queryParams.push(leadId);

        const updateQuery = `
            UPDATE leads 
            SET ${updateFields.join(', ')}
            WHERE id = $${paramIndex}
            RETURNING *
        `;

        const result = await query(updateQuery, queryParams);

        if (result.rows.length === 0) {
            return {
                success: false,
                message: 'Lead not found'
            };
        }

        return {
            success: true,
            message: 'Lead updated successfully',
            data: result.rows[0]
        };
    } catch (error) {
        console.error('Error updating lead:', error);
        return {
            success: false,
            message: 'Failed to update lead'
        };
    }
};

// Assign lead to agent
const assignLeadToAgent = async (leadId, agentId) => {
    try {
        // Verify agent exists and has appropriate role
        const agentResult = await query(
            'SELECT id, role FROM users WHERE id = $1 AND role IN ($2, $3) AND is_active = true',
            [agentId, 'call_center', 'reception']
        );

        if (agentResult.rows.length === 0) {
            return {
                success: false,
                message: 'Invalid agent or agent not authorized for lead assignment'
            };
        }

        const result = await query(
            `UPDATE leads 
             SET assigned_agent_id = $1, updated_at = CURRENT_TIMESTAMP
             WHERE id = $2
             RETURNING *`,
            [agentId, leadId]
        );

        if (result.rows.length === 0) {
            return {
                success: false,
                message: 'Lead not found'
            };
        }

        return {
            success: true,
            message: 'Lead assigned successfully',
            data: result.rows[0]
        };
    } catch (error) {
        console.error('Error assigning lead:', error);
        return {
            success: false,
            message: 'Failed to assign lead'
        };
    }
};

// Get lead statistics
const getLeadStats = async (filters = {}) => {
    try {
        const { date_from, date_to, agent_id } = filters;
        
        let whereConditions = [];
        let queryParams = [];
        let paramIndex = 1;

        if (date_from) {
            whereConditions.push(`created_at >= $${paramIndex++}`);
            queryParams.push(date_from);
        }

        if (date_to) {
            whereConditions.push(`created_at <= $${paramIndex++}`);
            queryParams.push(date_to);
        }

        if (agent_id) {
            whereConditions.push(`assigned_agent_id = $${paramIndex++}`);
            queryParams.push(agent_id);
        }

        const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

        const statsQuery = `
            SELECT 
                COUNT(*) as total_leads,
                COUNT(CASE WHEN status = 'new' THEN 1 END) as new_leads,
                COUNT(CASE WHEN status = 'contacted' THEN 1 END) as contacted_leads,
                COUNT(CASE WHEN status = 'interested' THEN 1 END) as interested_leads,
                COUNT(CASE WHEN status = 'enrolled' THEN 1 END) as enrolled_leads,
                COUNT(CASE WHEN status = 'lost' THEN 1 END) as lost_leads,
                COUNT(CASE WHEN source = 'landing_page' THEN 1 END) as landing_page_leads,
                ROUND(
                    COUNT(CASE WHEN status = 'enrolled' THEN 1 END) * 100.0 / 
                    NULLIF(COUNT(*), 0), 2
                ) as conversion_rate
            FROM leads
            ${whereClause}
        `;

        const result = await query(statsQuery, queryParams);

        return {
            success: true,
            data: result.rows[0]
        };
    } catch (error) {
        console.error('Error getting lead stats:', error);
        return {
            success: false,
            message: 'Failed to retrieve lead statistics'
        };
    }
};

module.exports = {
    createLead,
    getLeads,
    getLeadById,
    updateLead,
    assignLeadToAgent,
    getLeadStats
};
