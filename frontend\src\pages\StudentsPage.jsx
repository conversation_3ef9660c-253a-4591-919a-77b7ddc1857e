import React, { useState } from 'react'
import {
  Card,
  Table,
  Button,
  Input,
  Select,
  Space,
  Tag,
  Modal,
  Form,
  DatePicker,
  Typography,
  Row,
  Col,
  Statistic,
  Tooltip,
  message,
  Avatar,
  Drawer,
  Descriptions,
  Tabs
} from 'antd'
import {
  SearchOutlined,
  UserOutlined,
  PlusOutlined,
  EditOutlined,
  EyeOutlined,
  UsergroupAddOutlined,
  BookOutlined,
  DollarOutlined,
  ExperimentOutlined
} from '@ant-design/icons'
import { useQuery, useMutation, useQueryClient } from 'react-query'
import { useAuthStore } from '../stores/authStore'
import axios from 'axios'
import dayjs from 'dayjs'

const { Title, Text } = Typography
const { TextArea } = Input
const { TabPane } = Tabs

const StudentsPage = () => {
  const { user } = useAuthStore()
  const queryClient = useQueryClient()
  const [searchText, setSearchText] = useState('')
  const [levelFilter, setLevelFilter] = useState('')
  const [statusFilter, setStatusFilter] = useState('')
  const [selectedStudent, setSelectedStudent] = useState(null)
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [isDrawerVisible, setIsDrawerVisible] = useState(false)
  const [modalType, setModalType] = useState('') // 'create', 'edit'
  const [form] = Form.useForm()

  // Fetch students
  const { data: studentsData, isLoading } = useQuery(
    ['students', searchText, levelFilter, statusFilter],
    async () => {
      const params = new URLSearchParams()
      if (searchText) params.append('search', searchText)
      if (levelFilter) params.append('level', levelFilter)
      if (statusFilter) params.append('is_active', statusFilter)

      const response = await axios.get(`/students?${params}`)
      return response.data.data
    },
    {
      refetchInterval: 30000
    }
  )

  // Create student mutation
  const createStudentMutation = useMutation(
    async (data) => {
      const response = await axios.post('/students', data)
      return response.data
    },
    {
      onSuccess: () => {
        message.success('Student created successfully')
        queryClient.invalidateQueries('students')
        setIsModalVisible(false)
        form.resetFields()
      },
      onError: (error) => {
        message.error(error.response?.data?.message || 'Failed to create student')
      }
    }
  )

  // Update student mutation
  const updateStudentMutation = useMutation(
    async ({ studentId, data }) => {
      const response = await axios.put(`/students/${studentId}`, data)
      return response.data
    },
    {
      onSuccess: () => {
        message.success('Student updated successfully')
        queryClient.invalidateQueries('students')
        setIsModalVisible(false)
        form.resetFields()
      },
      onError: (error) => {
        message.error(error.response?.data?.message || 'Failed to update student')
      }
    }
  )

  const getLevelColor = (level) => {
    const colors = {
      A1: 'red',
      A2: 'orange',
      B1: 'gold',
      B2: 'lime',
      C1: 'green',
      C2: 'cyan'
    }
    return colors[level] || 'default'
  }

  const handleCreateStudent = () => {
    setModalType('create')
    form.resetFields()
    setIsModalVisible(true)
  }

  const handleEditStudent = (student) => {
    setSelectedStudent(student)
    setModalType('edit')
    form.setFieldsValue({
      ...student,
      date_of_birth: student.date_of_birth ? dayjs(student.date_of_birth) : null
    })
    setIsModalVisible(true)
  }

  const handleViewStudent = async (student) => {
    try {
      const response = await axios.get(`/students/${student.id}`)
      setSelectedStudent(response.data.data)
      setIsDrawerVisible(true)
    } catch (error) {
      message.error('Failed to load student details')
    }
  }

  const handleModalSubmit = async (values) => {
    if (modalType === 'create') {
      await createStudentMutation.mutateAsync({
        ...values,
        date_of_birth: values.date_of_birth ? values.date_of_birth.toISOString() : null
      })
    } else if (modalType === 'edit') {
      await updateStudentMutation.mutateAsync({
        studentId: selectedStudent.id,
        data: {
          ...values,
          date_of_birth: values.date_of_birth ? values.date_of_birth.toISOString() : null
        }
      })
    }
  }

  const columns = [
    {
      title: 'Student',
      key: 'student',
      render: (_, record) => (
        <Space>
          <Avatar
            src={record.photo_url}
            icon={<UserOutlined />}
            style={{ backgroundColor: '#0056b3' }}
          />
          <div>
            <Text strong>{record.first_name} {record.last_name}</Text>
            <br />
            <Text type="secondary" style={{ fontSize: 12 }}>
              ID: {record.student_id}
            </Text>
          </div>
        </Space>
      )
    },
    {
      title: 'Contact',
      key: 'contact',
      render: (_, record) => (
        <div>
          <Text copyable>{record.phone}</Text>
          <br />
          {record.email && (
            <Text type="secondary" style={{ fontSize: 12 }}>
              {record.email}
            </Text>
          )}
        </div>
      )
    },
    {
      title: 'Level',
      dataIndex: 'current_level',
      key: 'current_level',
      render: (level) => (
        level ? (
          <Tag color={getLevelColor(level)}>
            {level}
          </Tag>
        ) : (
          <Text type="secondary">Not Set</Text>
        )
      )
    },
    {
      title: 'Groups',
      dataIndex: 'active_groups',
      key: 'active_groups',
      render: (count) => (
        <Tag color={count > 0 ? 'blue' : 'default'}>
          {count} {count === 1 ? 'Group' : 'Groups'}
        </Tag>
      )
    },
    {
      title: 'Enrollment Date',
      dataIndex: 'enrollment_date',
      key: 'enrollment_date',
      render: (date) => dayjs(date).format('MMM DD, YYYY')
    },
    {
      title: 'Status',
      dataIndex: 'is_active',
      key: 'is_active',
      render: (isActive) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? 'Active' : 'Inactive'}
        </Tag>
      )
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Tooltip title="View Details">
            <Button
              size="small"
              icon={<EyeOutlined />}
              onClick={() => handleViewStudent(record)}
            />
          </Tooltip>
          <Tooltip title="Edit Student">
            <Button
              size="small"
              icon={<EditOutlined />}
              onClick={() => handleEditStudent(record)}
            />
          </Tooltip>
          <Tooltip title="Enroll in Group">
            <Button
              size="small"
              icon={<UsergroupAddOutlined />}
              onClick={() => {
                // Group enrollment functionality
                message.info('Group enrollment feature coming soon')
              }}
            />
          </Tooltip>
        </Space>
      )
    }
  ]

  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>Student Management</Title>
        <Text type="secondary">
          Manage student profiles, enrollments, and academic progress
        </Text>
      </div>

      {/* Statistics Cards */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="Total Students"
              value={studentsData?.students?.length || 0}
              prefix={<UserOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Active Students"
              value={studentsData?.students?.filter(s => s.is_active).length || 0}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="New This Month"
              value={studentsData?.students?.filter(s =>
                dayjs(s.enrollment_date).isAfter(dayjs().startOf('month'))
              ).length || 0}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="In Groups"
              value={studentsData?.students?.filter(s => s.active_groups > 0).length || 0}
              prefix={<UsergroupAddOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Filters and Actions */}
      <Card style={{ marginBottom: 16 }}>
        <Row gutter={16} align="middle">
          <Col span={6}>
            <Input
              placeholder="Search students..."
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              allowClear
            />
          </Col>
          <Col span={4}>
            <Select
              placeholder="Filter by level"
              value={levelFilter}
              onChange={setLevelFilter}
              allowClear
              style={{ width: '100%' }}
            >
              <Select.Option value="A1">A1</Select.Option>
              <Select.Option value="A2">A2</Select.Option>
              <Select.Option value="B1">B1</Select.Option>
              <Select.Option value="B2">B2</Select.Option>
              <Select.Option value="C1">C1</Select.Option>
              <Select.Option value="C2">C2</Select.Option>
            </Select>
          </Col>
          <Col span={4}>
            <Select
              placeholder="Filter by status"
              value={statusFilter}
              onChange={setStatusFilter}
              allowClear
              style={{ width: '100%' }}
            >
              <Select.Option value="true">Active</Select.Option>
              <Select.Option value="false">Inactive</Select.Option>
            </Select>
          </Col>
          <Col span={10} style={{ textAlign: 'right' }}>
            <Space>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleCreateStudent}
              >
                Add New Student
              </Button>
              <Button>
                Import Students
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* Students Table */}
      <Card>
        <Table
          columns={columns}
          dataSource={studentsData?.students || []}
          loading={isLoading}
          rowKey="id"
          pagination={{
            pageSize: 20,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `Total ${total} students`
          }}
        />
      </Card>

      {/* Create/Edit Student Modal */}
      <Modal
        title={modalType === 'create' ? 'Add New Student' : 'Edit Student'}
        open={isModalVisible}
        onCancel={() => {
          setIsModalVisible(false)
          form.resetFields()
        }}
        footer={null}
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleModalSubmit}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="first_name"
                label="First Name"
                rules={[{ required: true, message: 'Please enter first name' }]}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="last_name"
                label="Last Name"
                rules={[{ required: true, message: 'Please enter last name' }]}
              >
                <Input />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="phone"
                label="Phone Number"
                rules={[
                  { required: true, message: 'Please enter phone number' },
                  { pattern: /^\+?[1-9]\d{1,14}$/, message: 'Please enter a valid phone number' }
                ]}
              >
                <Input placeholder="+998901234567" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="email"
                label="Email"
                rules={[{ type: 'email', message: 'Please enter a valid email' }]}
              >
                <Input />
              </Form.Item>
            </Col>
          </Row>

          {modalType === 'create' && (
            <Form.Item
              name="password"
              label="Password"
              rules={[
                { required: true, message: 'Please enter password' },
                { min: 6, message: 'Password must be at least 6 characters' }
              ]}
            >
              <Input.Password />
            </Form.Item>
          )}

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item name="date_of_birth" label="Date of Birth">
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="gender" label="Gender">
                <Select>
                  <Select.Option value="male">Male</Select.Option>
                  <Select.Option value="female">Female</Select.Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="current_level" label="Current Level">
                <Select>
                  <Select.Option value="A1">A1</Select.Option>
                  <Select.Option value="A2">A2</Select.Option>
                  <Select.Option value="B1">B1</Select.Option>
                  <Select.Option value="B2">B2</Select.Option>
                  <Select.Option value="C1">C1</Select.Option>
                  <Select.Option value="C2">C2</Select.Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="address" label="Address">
            <TextArea rows={2} />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="emergency_contact_name" label="Emergency Contact Name">
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="emergency_contact_phone" label="Emergency Contact Phone">
                <Input />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="learning_goals" label="Learning Goals">
            <TextArea rows={3} />
          </Form.Item>

          <Form.Item name="previous_experience" label="Previous Experience">
            <TextArea rows={3} />
          </Form.Item>

          <Form.Item name="special_needs" label="Special Needs">
            <TextArea rows={2} />
          </Form.Item>

          {modalType === 'edit' && (
            <Form.Item name="is_active" valuePropName="checked">
              <input type="checkbox" /> Active Student
            </Form.Item>
          )}

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setIsModalVisible(false)}>
                Cancel
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={createStudentMutation.isLoading || updateStudentMutation.isLoading}
              >
                {modalType === 'create' ? 'Create Student' : 'Update Student'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* Student Details Drawer */}
      <Drawer
        title="Student Details"
        placement="right"
        onClose={() => setIsDrawerVisible(false)}
        open={isDrawerVisible}
        width={600}
      >
        {selectedStudent && (
          <Tabs defaultActiveKey="1">
            <TabPane tab="Profile" key="1">
              <Descriptions column={1} bordered>
                <Descriptions.Item label="Student ID">
                  {selectedStudent.student_id}
                </Descriptions.Item>
                <Descriptions.Item label="Name">
                  {selectedStudent.first_name} {selectedStudent.last_name}
                </Descriptions.Item>
                <Descriptions.Item label="Phone">
                  {selectedStudent.phone}
                </Descriptions.Item>
                <Descriptions.Item label="Email">
                  {selectedStudent.email || 'Not provided'}
                </Descriptions.Item>
                <Descriptions.Item label="Current Level">
                  {selectedStudent.current_level ? (
                    <Tag color={getLevelColor(selectedStudent.current_level)}>
                      {selectedStudent.current_level}
                    </Tag>
                  ) : 'Not set'}
                </Descriptions.Item>
                <Descriptions.Item label="Enrollment Date">
                  {dayjs(selectedStudent.enrollment_date).format('MMMM DD, YYYY')}
                </Descriptions.Item>
                <Descriptions.Item label="Status">
                  <Tag color={selectedStudent.is_active ? 'green' : 'red'}>
                    {selectedStudent.is_active ? 'Active' : 'Inactive'}
                  </Tag>
                </Descriptions.Item>
              </Descriptions>
            </TabPane>

            <TabPane tab="Groups" key="2">
              {selectedStudent.groups?.length > 0 ? (
                selectedStudent.groups.map(group => (
                  <Card key={group.id} size="small" style={{ marginBottom: 8 }}>
                    <div>
                      <Text strong>{group.name}</Text>
                      <Tag style={{ marginLeft: 8 }} color={group.status === 'active' ? 'green' : 'default'}>
                        {group.status}
                      </Tag>
                    </div>
                    <Text type="secondary">{group.course_name}</Text>
                    <br />
                    <Text type="secondary" style={{ fontSize: 12 }}>
                      Enrolled: {dayjs(group.enrollment_date).format('MMM DD, YYYY')}
                    </Text>
                  </Card>
                ))
              ) : (
                <Text type="secondary">No group enrollments</Text>
              )}
            </TabPane>

            <TabPane tab="Tests" key="3">
              {selectedStudent.test_results?.length > 0 ? (
                selectedStudent.test_results.map(test => (
                  <Card key={test.id} size="small" style={{ marginBottom: 8 }}>
                    <div>
                      <Text strong>{test.test_name}</Text>
                      <Tag style={{ marginLeft: 8 }} color={test.passed ? 'green' : 'red'}>
                        {test.passed ? 'Passed' : 'Failed'}
                      </Tag>
                    </div>
                    <Text>Score: {test.score}/{test.total_score} ({test.percentage}%)</Text>
                    <br />
                    <Text type="secondary" style={{ fontSize: 12 }}>
                      {dayjs(test.start_time).format('MMM DD, YYYY')}
                    </Text>
                  </Card>
                ))
              ) : (
                <Text type="secondary">No test results</Text>
              )}
            </TabPane>

            <TabPane tab="Payments" key="4">
              {selectedStudent.payments?.length > 0 ? (
                selectedStudent.payments.map(payment => (
                  <Card key={payment.id} size="small" style={{ marginBottom: 8 }}>
                    <div>
                      <Text strong>{payment.amount.toLocaleString()} {payment.currency}</Text>
                      <Tag style={{ marginLeft: 8 }} color={
                        payment.status === 'paid' ? 'green' :
                        payment.status === 'pending' ? 'orange' : 'red'
                      }>
                        {payment.status}
                      </Tag>
                    </div>
                    <Text type="secondary">{payment.group_name}</Text>
                    <br />
                    <Text type="secondary" style={{ fontSize: 12 }}>
                      Due: {dayjs(payment.due_date).format('MMM DD, YYYY')}
                    </Text>
                  </Card>
                ))
              ) : (
                <Text type="secondary">No payment records</Text>
              )}
            </TabPane>
          </Tabs>
        )}
      </Drawer>
    </div>
  )
}

export default StudentsPage
