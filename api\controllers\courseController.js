const { query } = require('../config/database');
const Joi = require('joi');

// Validation schemas
const createCourseSchema = Joi.object({
    name: Joi.string().required().min(3).max(255),
    code: Joi.string().required().min(2).max(50),
    type: Joi.string().valid('General English', 'IELTS', 'SAT', 'Math', 'Kids English', 'Business English').required(),
    level: Joi.string().valid('A1', 'A2', 'B1', 'B2', 'C1', 'C2', 'Foundation', 'Beginner', 'Intermediate', 'Advanced').required(),
    duration_weeks: Joi.number().integer().min(1).max(52).required(),
    classes_per_week: Joi.number().integer().min(1).max(7).required(),
    class_duration_minutes: Joi.number().integer().min(30).max(180).required(),
    min_students: Joi.number().integer().min(1).max(20).required(),
    max_students: Joi.number().integer().min(1).max(20).required(),
    price: Joi.number().min(0).required(),
    prerequisites: Joi.string().allow('').max(500),
    materials: Joi.string().allow('').max(1000),
    description: Joi.string().allow('').max(2000),
    is_active: Joi.boolean().optional()
});

const updateCourseSchema = Joi.object({
    name: Joi.string().min(3).max(255).optional(),
    code: Joi.string().min(2).max(50).optional(),
    type: Joi.string().valid('General English', 'IELTS', 'SAT', 'Math', 'Kids English', 'Business English').optional(),
    level: Joi.string().valid('A1', 'A2', 'B1', 'B2', 'C1', 'C2', 'Foundation', 'Beginner', 'Intermediate', 'Advanced').optional(),
    duration_weeks: Joi.number().integer().min(1).max(52).optional(),
    classes_per_week: Joi.number().integer().min(1).max(7).optional(),
    class_duration_minutes: Joi.number().integer().min(30).max(180).optional(),
    min_students: Joi.number().integer().min(1).max(20).optional(),
    max_students: Joi.number().integer().min(1).max(20).optional(),
    price: Joi.number().min(0).optional(),
    prerequisites: Joi.string().allow('').max(500).optional(),
    materials: Joi.string().allow('').max(1000).optional(),
    description: Joi.string().allow('').max(2000).optional(),
    is_active: Joi.boolean().optional()
});

// Get all courses with filtering
const getCourses = async (filters = {}) => {
    try {
        const { type, level, is_active, search } = filters;
        
        let whereConditions = [];
        let queryParams = [];
        let paramIndex = 1;

        if (type) {
            whereConditions.push(`type = $${paramIndex++}`);
            queryParams.push(type);
        }

        if (level) {
            whereConditions.push(`level = $${paramIndex++}`);
            queryParams.push(level);
        }

        if (is_active !== undefined) {
            whereConditions.push(`is_active = $${paramIndex++}`);
            queryParams.push(is_active === 'true');
        }

        if (search) {
            whereConditions.push(`(name ILIKE $${paramIndex} OR code ILIKE $${paramIndex})`);
            queryParams.push(`%${search}%`);
            paramIndex++;
        }

        const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

        const coursesQuery = `
            SELECT 
                c.*,
                COUNT(DISTINCT g.id) as active_groups,
                COUNT(DISTINCT s.id) as enrolled_students
            FROM courses c
            LEFT JOIN groups g ON c.id = g.course_id AND g.is_active = true
            LEFT JOIN student_groups sg ON g.id = sg.group_id
            LEFT JOIN students s ON sg.student_id = s.id
            ${whereClause}
            GROUP BY c.id
            ORDER BY c.type, c.level, c.name
        `;

        const result = await query(coursesQuery, queryParams);

        return {
            success: true,
            data: result.rows
        };
    } catch (error) {
        console.error('Get courses error:', error);
        return {
            success: false,
            message: 'Failed to retrieve courses'
        };
    }
};

// Get course by ID
const getCourseById = async (courseId) => {
    try {
        const courseQuery = `
            SELECT 
                c.*,
                COUNT(DISTINCT g.id) as active_groups,
                COUNT(DISTINCT s.id) as enrolled_students
            FROM courses c
            LEFT JOIN groups g ON c.id = g.course_id AND g.is_active = true
            LEFT JOIN student_groups sg ON g.id = sg.group_id
            LEFT JOIN students s ON sg.student_id = s.id
            WHERE c.id = $1
            GROUP BY c.id
        `;

        const result = await query(courseQuery, [courseId]);

        if (result.rows.length === 0) {
            return {
                success: false,
                message: 'Course not found'
            };
        }

        return {
            success: true,
            data: result.rows[0]
        };
    } catch (error) {
        console.error('Get course by ID error:', error);
        return {
            success: false,
            message: 'Failed to retrieve course'
        };
    }
};

// Create new course
const createCourse = async (courseData) => {
    try {
        // Validate input data
        const { error, value } = createCourseSchema.validate(courseData);
        if (error) {
            return {
                success: false,
                message: error.details[0].message
            };
        }

        const {
            name, code, type, level, duration_weeks, classes_per_week,
            class_duration_minutes, min_students, max_students, price,
            prerequisites, materials, description, is_active
        } = value;

        // Validate min_students <= max_students
        if (min_students > max_students) {
            return {
                success: false,
                message: 'Minimum students cannot be greater than maximum students'
            };
        }

        // Check if course code already exists
        const existingCourse = await query(
            'SELECT id FROM courses WHERE code = $1',
            [code]
        );

        if (existingCourse.rows.length > 0) {
            return {
                success: false,
                message: 'Course with this code already exists'
            };
        }

        const insertQuery = `
            INSERT INTO courses (
                name, code, type, level, duration_weeks, classes_per_week,
                class_duration_minutes, min_students, max_students, price,
                prerequisites, materials, description, is_active
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
            RETURNING *
        `;

        const result = await query(insertQuery, [
            name, code, type, level, duration_weeks, classes_per_week,
            class_duration_minutes, min_students, max_students, price,
            prerequisites || null, materials || null, description || null,
            is_active !== undefined ? is_active : true
        ]);

        return {
            success: true,
            data: result.rows[0],
            message: 'Course created successfully'
        };
    } catch (error) {
        console.error('Create course error:', error);
        return {
            success: false,
            message: 'Failed to create course'
        };
    }
};

// Update course
const updateCourse = async (courseId, courseData) => {
    try {
        // Validate input data
        const { error, value } = updateCourseSchema.validate(courseData);
        if (error) {
            return {
                success: false,
                message: error.details[0].message
            };
        }

        // Check if course exists
        const courseCheck = await query(
            'SELECT id FROM courses WHERE id = $1',
            [courseId]
        );

        if (courseCheck.rows.length === 0) {
            return {
                success: false,
                message: 'Course not found'
            };
        }

        // Validate min_students <= max_students if both are provided
        if (value.min_students && value.max_students && value.min_students > value.max_students) {
            return {
                success: false,
                message: 'Minimum students cannot be greater than maximum students'
            };
        }

        // Check if course code already exists (if code is being updated)
        if (value.code) {
            const existingCourse = await query(
                'SELECT id FROM courses WHERE code = $1 AND id != $2',
                [value.code, courseId]
            );

            if (existingCourse.rows.length > 0) {
                return {
                    success: false,
                    message: 'Course with this code already exists'
                };
            }
        }

        // Build update query dynamically
        const updateFields = [];
        const updateParams = [];
        let paramIndex = 1;

        Object.keys(value).forEach(field => {
            if (value[field] !== undefined) {
                updateFields.push(`${field} = $${paramIndex++}`);
                updateParams.push(value[field]);
            }
        });

        if (updateFields.length === 0) {
            return {
                success: false,
                message: 'No fields to update'
            };
        }

        updateFields.push(`updated_at = NOW()`);
        updateParams.push(courseId);

        const updateQuery = `
            UPDATE courses 
            SET ${updateFields.join(', ')}
            WHERE id = $${paramIndex}
            RETURNING *
        `;

        const result = await query(updateQuery, updateParams);

        return {
            success: true,
            data: result.rows[0],
            message: 'Course updated successfully'
        };
    } catch (error) {
        console.error('Update course error:', error);
        return {
            success: false,
            message: 'Failed to update course'
        };
    }
};

// Get course statistics
const getCourseStatistics = async () => {
    try {
        const statsQuery = `
            SELECT 
                COUNT(*) as total_courses,
                COUNT(CASE WHEN is_active = true THEN 1 END) as active_courses,
                AVG(price) as average_price,
                SUM(CASE WHEN is_active = true THEN 1 ELSE 0 END) as active_count,
                (SELECT COUNT(DISTINCT s.id) 
                 FROM students s 
                 JOIN student_groups sg ON s.id = sg.student_id 
                 JOIN groups g ON sg.group_id = g.id 
                 WHERE g.is_active = true) as total_enrolled
            FROM courses
        `;

        const result = await query(statsQuery);

        return {
            success: true,
            data: result.rows[0]
        };
    } catch (error) {
        console.error('Get course statistics error:', error);
        return {
            success: false,
            message: 'Failed to retrieve course statistics'
        };
    }
};

module.exports = {
    getCourses,
    getCourseById,
    createCourse,
    updateCourse,
    getCourseStatistics
};
