/*
   Innovative Centre - Jobs Page Stylesheet
   Author: Augment Agent
   Version: 1.0
*/

/* Jobs Hero Section */
.jobs-hero-section {
    background: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)), url('../images/jobs/jobs-hero.jpg') no-repeat center center/cover;
    height: 50vh;
    min-height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-white);
    padding-top: 80px;
    text-align: center;
}

.jobs-hero-content h1 {
    font-size: 48px;
    font-weight: 700;
    margin-bottom: 20px;
    color: #fff;
}

.jobs-hero-content p {
    font-size: 20px;
    max-width: 800px;
    margin: 0 auto;
    color: rgba(255, 255, 255, 0.9);
}

/* Jobs Section */
.jobs-section {
    padding: 80px 0;
    background-color: var(--bg-light);
}

.jobs-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
    margin-top: 50px;
}

.job-card {
    background-color: #fff;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: var(--transition);
    height: 100%;
    display: flex;
    flex-direction: column;
}

.job-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.job-image {
    height: 250px;
    overflow: hidden;
}

.job-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.job-card:hover .job-image img {
    transform: scale(1.05);
}

.job-content {
    padding: 25px;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.job-content h3 {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 15px;
    color: var(--primary-color);
}

.job-description {
    margin-bottom: 20px;
    flex-grow: 1;
}

.job-description p {
    margin-bottom: 15px;
    color: var(--text-light);
}

.job-description h4 {
    font-size: 18px;
    font-weight: 600;
    margin: 15px 0 10px;
    color: var(--text-color);
}

.job-description ul {
    padding-left: 20px;
    margin-bottom: 15px;
}

.job-description ul li {
    margin-bottom: 5px;
    position: relative;
    padding-left: 15px;
    list-style-type: none;
}

.job-description ul li:before {
    content: "•";
    color: var(--primary-color);
    position: absolute;
    left: 0;
}

.job-card .btn {
    align-self: flex-start;
    margin-top: auto;
}

/* Take Next Step Section */
.take-next-step {
    text-align: center;
    margin-top: 60px;
    padding: 40px;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: var(--shadow);
}

.take-next-step h2 {
    font-size: 32px;
    font-weight: 700;
    margin-bottom: 15px;
    color: var(--primary-color);
}

.take-next-step p {
    font-size: 18px;
    margin-bottom: 25px;
    color: var(--text-light);
}

.take-next-step .btn {
    padding: 15px 30px;
    font-size: 18px;
}

/* Responsive Styles */
@media (max-width: 991.98px) {
    .jobs-grid {
        grid-template-columns: 1fr;
    }

    .jobs-hero-content h1 {
        font-size: 40px;
    }
}

@media (max-width: 767.98px) {
    .jobs-hero-section {
        min-height: 300px;
    }

    .jobs-hero-content h1 {
        font-size: 32px;
    }

    .jobs-hero-content p {
        font-size: 16px;
    }

    .job-image {
        height: 200px;
    }

    .job-content {
        padding: 20px;
    }

    .job-content h3 {
        font-size: 20px;
    }

    .take-next-step {
        padding: 30px 20px;
    }

    .take-next-step h2 {
        font-size: 26px;
    }
}

/* Header Elements */
.header-phone, .header-social {
    color: var(--text-white);
    display: flex;
    align-items: center;
    gap: 5px;
    font-weight: 500;
    transition: var(--transition);
}

.header.scrolled .header-phone,
.header.scrolled .header-social {
    color: var(--text-color);
}

.header-phone:hover,
.header-social:hover {
    color: var(--secondary-color);
}

.header-social i {
    font-size: 18px;
}

/* Active Navigation Link */
.nav-link.active {
    color: var(--secondary-color);
}

.header.scrolled .nav-link.active {
    color: var(--secondary-color);
}
