const { query, transaction } = require('../config/database');
const Joi = require('joi');
const bcrypt = require('bcryptjs');

// Validation schemas
const createStudentSchema = Joi.object({
    // User information
    first_name: Joi.string().min(2).max(100).required(),
    last_name: Joi.string().min(2).max(100).required(),
    phone: Joi.string().pattern(/^\+?[1-9]\d{1,14}$/).required(),
    email: Joi.string().email().optional(),
    password: Joi.string().min(6).required(),
    
    // Student specific information
    date_of_birth: Joi.date().optional(),
    gender: Joi.string().valid('male', 'female').optional(),
    address: Joi.string().max(500).optional(),
    emergency_contact_name: Joi.string().max(200).optional(),
    emergency_contact_phone: Joi.string().pattern(/^\+?[1-9]\d{1,14}$/).optional(),
    initial_level: Joi.string().valid('A1', 'A2', 'B1', 'B2', 'C1', 'C2').optional(),
    learning_goals: Joi.string().max(1000).optional(),
    previous_experience: Joi.string().max(1000).optional(),
    special_needs: Joi.string().max(500).optional(),
    parent_id: Joi.string().uuid().optional(),
    lead_id: Joi.string().uuid().optional()
});

const updateStudentSchema = Joi.object({
    first_name: Joi.string().min(2).max(100).optional(),
    last_name: Joi.string().min(2).max(100).optional(),
    phone: Joi.string().pattern(/^\+?[1-9]\d{1,14}$/).optional(),
    email: Joi.string().email().allow('').optional(),
    date_of_birth: Joi.date().allow(null).optional(),
    gender: Joi.string().valid('male', 'female').allow('').optional(),
    address: Joi.string().max(500).allow('').optional(),
    emergency_contact_name: Joi.string().max(200).allow('').optional(),
    emergency_contact_phone: Joi.string().pattern(/^\+?[1-9]\d{1,14}$/).allow('').optional(),
    current_level: Joi.string().valid('A1', 'A2', 'B1', 'B2', 'C1', 'C2').optional(),
    learning_goals: Joi.string().max(1000).allow('').optional(),
    previous_experience: Joi.string().max(1000).allow('').optional(),
    special_needs: Joi.string().max(500).allow('').optional(),
    is_active: Joi.boolean().optional()
});

// Generate unique student ID
const generateStudentId = async () => {
    const year = new Date().getFullYear().toString().slice(-2);
    const month = (new Date().getMonth() + 1).toString().padStart(2, '0');
    
    // Get the last student ID for this month
    const lastStudent = await query(
        `SELECT student_id FROM students 
         WHERE student_id LIKE $1 
         ORDER BY student_id DESC LIMIT 1`,
        [`IC${year}${month}%`]
    );

    let sequence = 1;
    if (lastStudent.rows.length > 0) {
        const lastId = lastStudent.rows[0].student_id;
        const lastSequence = parseInt(lastId.slice(-4));
        sequence = lastSequence + 1;
    }

    return `IC${year}${month}${sequence.toString().padStart(4, '0')}`;
};

// Create new student
const createStudent = async (studentData) => {
    try {
        // Validate input data
        const { error, value } = createStudentSchema.validate(studentData);
        if (error) {
            return {
                success: false,
                message: error.details[0].message
            };
        }

        const {
            first_name, last_name, phone, email, password,
            date_of_birth, gender, address, emergency_contact_name,
            emergency_contact_phone, initial_level, learning_goals,
            previous_experience, special_needs, parent_id, lead_id
        } = value;

        // Check if user with same phone already exists
        const existingUser = await query(
            'SELECT id FROM users WHERE phone = $1',
            [phone]
        );

        if (existingUser.rows.length > 0) {
            return {
                success: false,
                message: 'User with this phone number already exists'
            };
        }

        // Generate student ID
        const studentId = await generateStudentId();

        // Hash password
        const saltRounds = parseInt(process.env.BCRYPT_ROUNDS) || 12;
        const password_hash = await bcrypt.hash(password, saltRounds);

        // Create student in transaction
        const result = await transaction(async (client) => {
            // Create user account
            const userResult = await client.query(
                `INSERT INTO users (email, phone, password_hash, first_name, last_name, role)
                 VALUES ($1, $2, $3, $4, $5, 'student')
                 RETURNING id`,
                [email, phone, password_hash, first_name, last_name]
            );

            const userId = userResult.rows[0].id;

            // Create student profile
            const studentResult = await client.query(
                `INSERT INTO students (
                    user_id, lead_id, student_id, date_of_birth, gender, address,
                    emergency_contact_name, emergency_contact_phone, current_level,
                    initial_level, learning_goals, previous_experience, special_needs,
                    parent_id, enrollment_date
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, CURRENT_DATE)
                 RETURNING *`,
                [
                    userId, lead_id, studentId, date_of_birth, gender, address,
                    emergency_contact_name, emergency_contact_phone, initial_level,
                    initial_level, learning_goals, previous_experience, special_needs,
                    parent_id
                ]
            );

            // If created from lead, update lead status
            if (lead_id) {
                await client.query(
                    'UPDATE leads SET status = $1 WHERE id = $2',
                    ['enrolled', lead_id]
                );
            }

            return {
                user: { id: userId, first_name, last_name, phone, email },
                student: studentResult.rows[0]
            };
        });

        return {
            success: true,
            message: 'Student created successfully',
            data: result
        };
    } catch (error) {
        console.error('Error creating student:', error);
        return {
            success: false,
            message: 'Failed to create student'
        };
    }
};

// Get all students with filtering and pagination
const getStudents = async (filters = {}, pagination = {}) => {
    try {
        const {
            search,
            level,
            is_active,
            group_id,
            enrollment_date_from,
            enrollment_date_to
        } = filters;

        const {
            page = 1,
            limit = 20,
            sort_by = 'enrollment_date',
            sort_order = 'DESC'
        } = pagination;

        let whereConditions = [];
        let queryParams = [];
        let paramIndex = 1;

        // Build WHERE conditions
        if (search) {
            whereConditions.push(`(
                u.first_name ILIKE $${paramIndex} OR 
                u.last_name ILIKE $${paramIndex} OR 
                s.student_id ILIKE $${paramIndex} OR
                u.phone ILIKE $${paramIndex}
            )`);
            queryParams.push(`%${search}%`);
            paramIndex++;
        }

        if (level) {
            whereConditions.push(`s.current_level = $${paramIndex++}`);
            queryParams.push(level);
        }

        if (is_active !== undefined) {
            whereConditions.push(`s.is_active = $${paramIndex++}`);
            queryParams.push(is_active === 'true');
        }

        if (group_id) {
            whereConditions.push(`EXISTS (
                SELECT 1 FROM group_enrollments ge 
                WHERE ge.student_id = s.id 
                AND ge.group_id = $${paramIndex} 
                AND ge.status = 'active'
            )`);
            queryParams.push(group_id);
            paramIndex++;
        }

        if (enrollment_date_from) {
            whereConditions.push(`s.enrollment_date >= $${paramIndex++}`);
            queryParams.push(enrollment_date_from);
        }

        if (enrollment_date_to) {
            whereConditions.push(`s.enrollment_date <= $${paramIndex++}`);
            queryParams.push(enrollment_date_to);
        }

        const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

        // Count total records
        const countQuery = `
            SELECT COUNT(*) as total
            FROM students s
            JOIN users u ON s.user_id = u.id
            ${whereClause}
        `;
        const countResult = await query(countQuery, queryParams);
        const total = parseInt(countResult.rows[0].total);

        // Calculate pagination
        const offset = (page - 1) * limit;
        const totalPages = Math.ceil(total / limit);

        // Get students with related information
        const studentsQuery = `
            SELECT 
                s.id,
                s.student_id,
                s.enrollment_date,
                s.current_level,
                s.initial_level,
                s.is_active,
                s.photo_url,
                u.first_name,
                u.last_name,
                u.phone,
                u.email,
                parent.first_name as parent_first_name,
                parent.last_name as parent_last_name,
                parent.phone as parent_phone,
                COUNT(DISTINCT ge.group_id) as active_groups
            FROM students s
            JOIN users u ON s.user_id = u.id
            LEFT JOIN users parent ON s.parent_id = parent.id
            LEFT JOIN group_enrollments ge ON s.id = ge.student_id AND ge.status = 'active'
            ${whereClause}
            GROUP BY s.id, u.id, parent.id
            ORDER BY s.${sort_by} ${sort_order}
            LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
        `;

        queryParams.push(limit, offset);
        const studentsResult = await query(studentsQuery, queryParams);

        return {
            success: true,
            data: {
                students: studentsResult.rows,
                pagination: {
                    current_page: page,
                    total_pages: totalPages,
                    total_records: total,
                    records_per_page: limit,
                    has_next: page < totalPages,
                    has_prev: page > 1
                }
            }
        };
    } catch (error) {
        console.error('Error getting students:', error);
        return {
            success: false,
            message: 'Failed to retrieve students'
        };
    }
};

// Get student by ID with detailed information
const getStudentById = async (studentId) => {
    try {
        const studentQuery = `
            SELECT 
                s.*,
                u.first_name,
                u.last_name,
                u.email,
                u.phone,
                u.last_login,
                parent.first_name as parent_first_name,
                parent.last_name as parent_last_name,
                parent.phone as parent_phone,
                parent.email as parent_email
            FROM students s
            JOIN users u ON s.user_id = u.id
            LEFT JOIN users parent ON s.parent_id = parent.id
            WHERE s.id = $1
        `;

        const result = await query(studentQuery, [studentId]);

        if (result.rows.length === 0) {
            return {
                success: false,
                message: 'Student not found'
            };
        }

        const student = result.rows[0];

        // Get student's group enrollments
        const groupsQuery = `
            SELECT 
                g.id,
                g.name,
                g.code,
                c.name as course_name,
                c.type as course_type,
                c.level as course_level,
                ge.enrollment_date,
                ge.status,
                ge.final_grade,
                t.user_id as teacher_user_id,
                tu.first_name as teacher_first_name,
                tu.last_name as teacher_last_name
            FROM group_enrollments ge
            JOIN groups g ON ge.group_id = g.id
            LEFT JOIN courses c ON g.course_id = c.id
            LEFT JOIN teachers t ON g.teacher_id = t.id
            LEFT JOIN users tu ON t.user_id = tu.id
            WHERE ge.student_id = $1
            ORDER BY ge.enrollment_date DESC
        `;

        const groupsResult = await query(groupsQuery, [studentId]);

        // Get student's test results
        const testsQuery = `
            SELECT 
                tr.id,
                tr.score,
                tr.percentage,
                tr.skill_breakdown,
                tr.start_time,
                tr.end_time,
                tr.passed,
                tr.certificate_url,
                t.name as test_name,
                t.type as test_type,
                t.level as test_level,
                t.total_score
            FROM test_results tr
            JOIN tests t ON tr.test_id = t.id
            WHERE tr.student_id = $1
            ORDER BY tr.start_time DESC
            LIMIT 10
        `;

        const testsResult = await query(testsQuery, [studentId]);

        // Get payment history
        const paymentsQuery = `
            SELECT 
                p.id,
                p.amount,
                p.currency,
                p.status,
                p.payment_method,
                p.due_date,
                p.paid_date,
                p.created_at,
                g.name as group_name
            FROM payments p
            LEFT JOIN groups g ON p.group_id = g.id
            WHERE p.student_id = $1
            ORDER BY p.created_at DESC
            LIMIT 10
        `;

        const paymentsResult = await query(paymentsQuery, [studentId]);

        return {
            success: true,
            data: {
                ...student,
                groups: groupsResult.rows,
                test_results: testsResult.rows,
                payments: paymentsResult.rows
            }
        };
    } catch (error) {
        console.error('Error getting student by ID:', error);
        return {
            success: false,
            message: 'Failed to retrieve student'
        };
    }
};

// Update student
const updateStudent = async (studentId, updateData) => {
    try {
        // Validate input data
        const { error, value } = updateStudentSchema.validate(updateData);
        if (error) {
            return {
                success: false,
                message: error.details[0].message
            };
        }

        // Separate user fields from student fields
        const userFields = ['first_name', 'last_name', 'phone', 'email'];
        const studentFields = [
            'date_of_birth', 'gender', 'address', 'emergency_contact_name',
            'emergency_contact_phone', 'current_level', 'learning_goals',
            'previous_experience', 'special_needs', 'is_active'
        ];

        const userUpdateData = {};
        const studentUpdateData = {};

        Object.keys(value).forEach(key => {
            if (userFields.includes(key)) {
                userUpdateData[key] = value[key];
            } else if (studentFields.includes(key)) {
                studentUpdateData[key] = value[key];
            }
        });

        const result = await transaction(async (client) => {
            let updatedUser = null;
            let updatedStudent = null;

            // Update user information if needed
            if (Object.keys(userUpdateData).length > 0) {
                const userUpdateFields = [];
                const userQueryParams = [];
                let userParamIndex = 1;

                Object.keys(userUpdateData).forEach(key => {
                    userUpdateFields.push(`${key} = $${userParamIndex++}`);
                    userQueryParams.push(userUpdateData[key]);
                });

                userUpdateFields.push(`updated_at = CURRENT_TIMESTAMP`);
                userQueryParams.push(studentId);

                const userUpdateQuery = `
                    UPDATE users 
                    SET ${userUpdateFields.join(', ')}
                    WHERE id = (SELECT user_id FROM students WHERE id = $${userParamIndex})
                    RETURNING *
                `;

                const userResult = await client.query(userUpdateQuery, userQueryParams);
                updatedUser = userResult.rows[0];
            }

            // Update student information if needed
            if (Object.keys(studentUpdateData).length > 0) {
                const studentUpdateFields = [];
                const studentQueryParams = [];
                let studentParamIndex = 1;

                Object.keys(studentUpdateData).forEach(key => {
                    studentUpdateFields.push(`${key} = $${studentParamIndex++}`);
                    studentQueryParams.push(studentUpdateData[key]);
                });

                studentUpdateFields.push(`updated_at = CURRENT_TIMESTAMP`);
                studentQueryParams.push(studentId);

                const studentUpdateQuery = `
                    UPDATE students 
                    SET ${studentUpdateFields.join(', ')}
                    WHERE id = $${studentParamIndex}
                    RETURNING *
                `;

                const studentResult = await client.query(studentUpdateQuery, studentQueryParams);
                updatedStudent = studentResult.rows[0];
            }

            return { user: updatedUser, student: updatedStudent };
        });

        return {
            success: true,
            message: 'Student updated successfully',
            data: result
        };
    } catch (error) {
        console.error('Error updating student:', error);
        return {
            success: false,
            message: 'Failed to update student'
        };
    }
};

// Enroll student in group
const enrollStudentInGroup = async (studentId, groupId) => {
    try {
        // Check if group exists and has capacity
        const groupCheck = await query(
            'SELECT max_capacity, current_enrollment FROM groups WHERE id = $1 AND status = $2',
            [groupId, 'active']
        );

        if (groupCheck.rows.length === 0) {
            return {
                success: false,
                message: 'Group not found or inactive'
            };
        }

        const group = groupCheck.rows[0];
        if (group.current_enrollment >= group.max_capacity) {
            return {
                success: false,
                message: 'Group is at maximum capacity'
            };
        }

        // Check if student is already enrolled
        const enrollmentCheck = await query(
            'SELECT id FROM group_enrollments WHERE student_id = $1 AND group_id = $2 AND status = $3',
            [studentId, groupId, 'active']
        );

        if (enrollmentCheck.rows.length > 0) {
            return {
                success: false,
                message: 'Student is already enrolled in this group'
            };
        }

        // Enroll student
        await transaction(async (client) => {
            await client.query(
                'INSERT INTO group_enrollments (student_id, group_id, status) VALUES ($1, $2, $3)',
                [studentId, groupId, 'active']
            );

            await client.query(
                'UPDATE groups SET current_enrollment = current_enrollment + 1 WHERE id = $1',
                [groupId]
            );
        });

        return {
            success: true,
            message: 'Student enrolled in group successfully'
        };
    } catch (error) {
        console.error('Error enrolling student in group:', error);
        return {
            success: false,
            message: 'Failed to enroll student in group'
        };
    }
};

module.exports = {
    createStudent,
    getStudents,
    getStudentById,
    updateStudent,
    enrollStudentInGroup
};
