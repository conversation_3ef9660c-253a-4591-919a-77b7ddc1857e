import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import axios from 'axios'
import toast from 'react-hot-toast'

// Configure axios defaults
axios.defaults.baseURL = '/api'

const useAuthStore = create(
  persist(
    (set, get) => ({
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: false,

      // Login action
      login: async (credentials) => {
        try {
          set({ isLoading: true })
          
          const response = await axios.post('/auth/login', credentials)
          
          if (response.data.success) {
            const { user, token } = response.data.data
            
            // Set axios default authorization header
            axios.defaults.headers.common['Authorization'] = `Bearer ${token}`
            
            set({
              user,
              token,
              isAuthenticated: true,
              isLoading: false
            })
            
            toast.success('Login successful!')
            return { success: true }
          } else {
            throw new Error(response.data.message || 'Login failed')
          }
        } catch (error) {
          set({ isLoading: false })
          const message = error.response?.data?.message || error.message || 'Login failed'
          toast.error(message)
          return { success: false, message }
        }
      },

      // Logout action
      logout: async () => {
        try {
          // Call logout endpoint
          await axios.post('/auth/logout')
        } catch (error) {
          console.error('Logout error:', error)
        } finally {
          // Clear auth data regardless of API call result
          delete axios.defaults.headers.common['Authorization']
          set({
            user: null,
            token: null,
            isAuthenticated: false,
            isLoading: false
          })
          toast.success('Logged out successfully')
        }
      },

      // Verify token and get user info
      verifyToken: async () => {
        const { token } = get()
        
        if (!token) {
          return false
        }

        try {
          // Set authorization header
          axios.defaults.headers.common['Authorization'] = `Bearer ${token}`
          
          const response = await axios.get('/auth/verify')
          
          if (response.data.success) {
            set({
              user: response.data.data.user,
              isAuthenticated: true
            })
            return true
          } else {
            throw new Error('Token verification failed')
          }
        } catch (error) {
          console.error('Token verification error:', error)
          // Clear invalid token
          delete axios.defaults.headers.common['Authorization']
          set({
            user: null,
            token: null,
            isAuthenticated: false
          })
          return false
        }
      },

      // Update user profile
      updateProfile: async (profileData) => {
        try {
          set({ isLoading: true })
          
          const response = await axios.put('/auth/profile', profileData)
          
          if (response.data.success) {
            set({
              user: response.data.data,
              isLoading: false
            })
            toast.success('Profile updated successfully!')
            return { success: true }
          } else {
            throw new Error(response.data.message || 'Profile update failed')
          }
        } catch (error) {
          set({ isLoading: false })
          const message = error.response?.data?.message || error.message || 'Profile update failed'
          toast.error(message)
          return { success: false, message }
        }
      },

      // Change password
      changePassword: async (passwordData) => {
        try {
          set({ isLoading: true })
          
          const response = await axios.post('/auth/change-password', passwordData)
          
          if (response.data.success) {
            set({ isLoading: false })
            toast.success('Password changed successfully!')
            return { success: true }
          } else {
            throw new Error(response.data.message || 'Password change failed')
          }
        } catch (error) {
          set({ isLoading: false })
          const message = error.response?.data?.message || error.message || 'Password change failed'
          toast.error(message)
          return { success: false, message }
        }
      },

      // Initialize auth state (call on app start)
      initialize: async () => {
        const { token, verifyToken } = get()
        
        if (token) {
          await verifyToken()
        }
      },

      // Check if user has permission
      hasPermission: (permission) => {
        const { user } = get()
        if (!user) return false

        // Admin has all permissions
        if (user.role === 'admin') return true

        // Define role permissions
        const rolePermissions = {
          reception: [
            'leads:read', 'leads:create', 'leads:update',
            'students:read', 'students:create', 'students:update',
            'groups:read', 'groups:update',
            'payments:read', 'payments:create'
          ],
          testing_department: [
            'tests:read', 'tests:create', 'tests:update',
            'test_results:read', 'test_results:create', 'test_results:update',
            'students:read', 'students:update'
          ],
          call_center: [
            'leads:read', 'leads:update',
            'calls:read', 'calls:create', 'calls:update',
            'students:read'
          ],
          cashier: [
            'payments:read', 'payments:create', 'payments:update',
            'students:read'
          ],
          teacher: [
            'groups:read',
            'students:read', 'students:update',
            'attendance:read', 'attendance:create', 'attendance:update',
            'tests:read', 'test_results:read', 'test_results:create'
          ],
          student: [
            'profile:read', 'profile:update',
            'tests:read', 'test_results:read',
            'payments:read', 'attendance:read'
          ],
          parent: [
            'child:read',
            'child_tests:read', 'child_payments:read', 'child_attendance:read'
          ]
        }

        const userPermissions = rolePermissions[user.role] || []
        return userPermissions.includes(permission)
      }
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated
      })
    }
  )
)

export { useAuthStore }
