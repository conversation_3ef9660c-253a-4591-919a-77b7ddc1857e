import React, { useState } from 'react'
import {
  Card, Typo<PERSON>, Button, Space, Table, Modal, Form, Input, Select,
  Tag, Tooltip, Row, Col, Statistic, Tabs, message, Drawer, Descriptions,
  Avatar, Rate, Progress, Badge, DatePicker, TimePicker, Upload
} from 'antd'
import {
  TeamOutlined, PlusOutlined, EyeOutlined, EditOutlined, UserOutlined,
  CalendarOutlined, TrophyOutlined, BookOutlined, DollarOutlined,
  UploadOutlined, PhoneOutlined, MailOutlined, StarOutlined
} from '@ant-design/icons'
import { useQuery, useMutation, useQueryClient } from 'react-query'
import axios from 'axios'
import dayjs from 'dayjs'

const { Title, Text } = Typography
const { TabPane } = Tabs
const { Option } = Select

const TeachersPage = () => {
  const [selectedTeacher, setSelectedTeacher] = useState(null)
  const [isTeacherModalVisible, setIsTeacherModalVisible] = useState(false)
  const [isScheduleModalVisible, setIsScheduleModalVisible] = useState(false)
  const [isTeacherDrawerVisible, setIsTeacherDrawerVisible] = useState(false)
  const [teacherForm] = Form.useForm()
  const [scheduleForm] = Form.useForm()
  const queryClient = useQueryClient()

  // Fetch teachers data
  const { data: teachersData, isLoading: teachersLoading } = useQuery(
    'teachers',
    async () => {
      const response = await axios.get('/teachers')
      return response.data.data
    }
  )

  // Fetch teacher schedules
  const { data: schedulesData, isLoading: schedulesLoading } = useQuery(
    'teacher-schedules',
    async () => {
      const response = await axios.get('/teachers/schedules')
      return response.data.data
    }
  )

  // Create teacher mutation
  const createTeacherMutation = useMutation(
    async (teacherData) => {
      const response = await axios.post('/teachers', teacherData)
      return response.data
    },
    {
      onSuccess: () => {
        message.success('Teacher created successfully')
        setIsTeacherModalVisible(false)
        teacherForm.resetFields()
        queryClient.invalidateQueries('teachers')
      },
      onError: (error) => {
        message.error(error.response?.data?.message || 'Failed to create teacher')
      }
    }
  )

  // Update teacher mutation
  const updateTeacherMutation = useMutation(
    async ({ id, ...teacherData }) => {
      const response = await axios.put(`/teachers/${id}`, teacherData)
      return response.data
    },
    {
      onSuccess: () => {
        message.success('Teacher updated successfully')
        setIsTeacherModalVisible(false)
        teacherForm.resetFields()
        queryClient.invalidateQueries('teachers')
      },
      onError: (error) => {
        message.error(error.response?.data?.message || 'Failed to update teacher')
      }
    }
  )

  const handleCreateTeacher = () => {
    setSelectedTeacher(null)
    setIsTeacherModalVisible(true)
    teacherForm.resetFields()
  }

  const handleEditTeacher = (teacher) => {
    setSelectedTeacher(teacher)
    setIsTeacherModalVisible(true)
    teacherForm.setFieldsValue({
      ...teacher,
      specializations: teacher.specializations?.split(',') || []
    })
  }

  const handleViewTeacher = (teacher) => {
    setSelectedTeacher(teacher)
    setIsTeacherDrawerVisible(true)
  }

  const handleTeacherSubmit = async (values) => {
    const teacherData = {
      ...values,
      specializations: values.specializations?.join(',') || ''
    }

    if (selectedTeacher) {
      updateTeacherMutation.mutate({ id: selectedTeacher.id, ...teacherData })
    } else {
      createTeacherMutation.mutate(teacherData)
    }
  }

  // Statistics data
  const statsData = [
    {
      title: 'Total Teachers',
      value: teachersData?.length || 0,
      icon: <TeamOutlined style={{ color: '#1890ff' }} />
    },
    {
      title: 'Active Teachers',
      value: teachersData?.filter(t => t.is_active).length || 0,
      icon: <UserOutlined style={{ color: '#52c41a' }} />
    },
    {
      title: 'Classes Today',
      value: schedulesData?.filter(s =>
        dayjs(s.date).isSame(dayjs(), 'day')
      ).length || 0,
      icon: <CalendarOutlined style={{ color: '#faad14' }} />
    },
    {
      title: 'Avg Rating',
      value: teachersData?.length > 0 ?
        (teachersData.reduce((sum, t) => sum + (t.rating || 0), 0) / teachersData.length).toFixed(1) : 0,
      icon: <StarOutlined style={{ color: '#ff7a45' }} />
    }
  ]

  // Teachers table columns
  const teachersColumns = [
    {
      title: 'Teacher',
      key: 'teacher',
      render: (_, record) => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Avatar
            size={40}
            src={record.photo_url}
            icon={<UserOutlined />}
            style={{ marginRight: 12 }}
          />
          <div>
            <Text strong>{record.first_name} {record.last_name}</Text>
            <br />
            <Text type="secondary" style={{ fontSize: 12 }}>
              ID: {record.employee_id}
            </Text>
          </div>
        </div>
      )
    },
    {
      title: 'Contact',
      key: 'contact',
      render: (_, record) => (
        <div>
          <div style={{ marginBottom: 4 }}>
            <PhoneOutlined style={{ marginRight: 4, color: '#1890ff' }} />
            <Text>{record.phone}</Text>
          </div>
          {record.email && (
            <div>
              <MailOutlined style={{ marginRight: 4, color: '#52c41a' }} />
              <Text type="secondary">{record.email}</Text>
            </div>
          )}
        </div>
      )
    },
    {
      title: 'Specializations',
      dataIndex: 'specializations',
      key: 'specializations',
      render: (specializations) => (
        <div>
          {specializations?.split(',').map((spec, index) => (
            <Tag key={index} color="blue" style={{ marginBottom: 4 }}>
              {spec.trim()}
            </Tag>
          ))}
        </div>
      )
    },
    {
      title: 'Experience',
      dataIndex: 'experience_years',
      key: 'experience_years',
      render: (years) => (
        <div>
          <Text strong>{years}</Text>
          <Text type="secondary"> years</Text>
        </div>
      )
    },
    {
      title: 'Rating',
      key: 'rating',
      render: (_, record) => (
        <div>
          <Rate disabled defaultValue={record.rating || 0} style={{ fontSize: 14 }} />
          <br />
          <Text type="secondary" style={{ fontSize: 12 }}>
            {record.rating ? `${record.rating}/5` : 'No rating'}
          </Text>
        </div>
      )
    },
    {
      title: 'Status',
      dataIndex: 'is_active',
      key: 'is_active',
      render: (isActive) => (
        <Badge
          status={isActive ? 'success' : 'default'}
          text={isActive ? 'Active' : 'Inactive'}
        />
      )
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Tooltip title="View Profile">
            <Button
              size="small"
              icon={<EyeOutlined />}
              onClick={() => handleViewTeacher(record)}
            />
          </Tooltip>
          <Tooltip title="Edit Teacher">
            <Button
              size="small"
              icon={<EditOutlined />}
              onClick={() => handleEditTeacher(record)}
            />
          </Tooltip>
          <Tooltip title="Manage Schedule">
            <Button
              size="small"
              icon={<CalendarOutlined />}
              onClick={() => {
                setSelectedTeacher(record)
                setIsScheduleModalVisible(true)
              }}
            />
          </Tooltip>
        </Space>
      )
    }
  ]

  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>Teacher Management</Title>
        <Text type="secondary">
          Manage teacher profiles, qualifications, and performance tracking
        </Text>
      </div>

      {/* Statistics Cards */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        {statsData.map((stat, index) => (
          <Col span={6} key={index}>
            <Card>
              <Statistic
                title={stat.title}
                value={stat.value}
                prefix={stat.icon}
                valueStyle={{ color: stat.icon.props.style.color }}
              />
            </Card>
          </Col>
        ))}
      </Row>

      {/* Action Buttons */}
      <Card style={{ marginBottom: 24 }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Space>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleCreateTeacher}
              >
                Add New Teacher
              </Button>
              <Button icon={<CalendarOutlined />}>
                Manage Schedules
              </Button>
              <Button icon={<TrophyOutlined />}>
                Performance Reports
              </Button>
            </Space>
          </Col>
          <Col>
            <Space>
              <Button>Export Data</Button>
              <Button>Import Teachers</Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* Teachers Table */}
      <Card>
        <Table
          columns={teachersColumns}
          dataSource={teachersData || []}
          loading={teachersLoading}
          rowKey="id"
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `Total ${total} teachers`
          }}
        />
      </Card>

      {/* Create/Edit Teacher Modal */}
      <Modal
        title={selectedTeacher ? 'Edit Teacher' : 'Add New Teacher'}
        open={isTeacherModalVisible}
        onCancel={() => setIsTeacherModalVisible(false)}
        footer={null}
        width={800}
      >
        <Form
          form={teacherForm}
          layout="vertical"
          onFinish={handleTeacherSubmit}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="first_name"
                label="First Name"
                rules={[{ required: true, message: 'Please enter first name' }]}
              >
                <Input placeholder="Enter first name" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="last_name"
                label="Last Name"
                rules={[{ required: true, message: 'Please enter last name' }]}
              >
                <Input placeholder="Enter last name" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="phone"
                label="Phone Number"
                rules={[{ required: true, message: 'Please enter phone number' }]}
              >
                <Input placeholder="+998 XX XXX XX XX" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="email"
                label="Email"
                rules={[{ type: 'email', message: 'Please enter valid email' }]}
              >
                <Input placeholder="<EMAIL>" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="employee_id"
                label="Employee ID"
                rules={[{ required: true, message: 'Please enter employee ID' }]}
              >
                <Input placeholder="T001" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="experience_years"
                label="Years of Experience"
                rules={[{ required: true, message: 'Please enter experience years' }]}
              >
                <Input type="number" placeholder="5" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="specializations"
                label="Specializations"
                rules={[{ required: true, message: 'Please select specializations' }]}
              >
                <Select
                  mode="multiple"
                  placeholder="Select specializations"
                  options={[
                    { label: 'General English', value: 'General English' },
                    { label: 'IELTS', value: 'IELTS' },
                    { label: 'SAT', value: 'SAT' },
                    { label: 'Kids English', value: 'Kids English' },
                    { label: 'Business English', value: 'Business English' },
                    { label: 'Conversation', value: 'Conversation' }
                  ]}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="qualification"
                label="Qualification"
              >
                <Input placeholder="Bachelor's in English Literature" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="bio"
            label="Biography"
          >
            <Input.TextArea
              rows={3}
              placeholder="Brief description about the teacher..."
            />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setIsTeacherModalVisible(false)}>
                Cancel
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={createTeacherMutation.isLoading || updateTeacherMutation.isLoading}
              >
                {selectedTeacher ? 'Update Teacher' : 'Create Teacher'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* Teacher Profile Drawer */}
      <Drawer
        title="Teacher Profile"
        placement="right"
        onClose={() => setIsTeacherDrawerVisible(false)}
        open={isTeacherDrawerVisible}
        width={600}
      >
        {selectedTeacher && (
          <div>
            <div style={{ textAlign: 'center', marginBottom: 24 }}>
              <Avatar
                size={80}
                src={selectedTeacher.photo_url}
                icon={<UserOutlined />}
                style={{ marginBottom: 16 }}
              />
              <Title level={4}>
                {selectedTeacher.first_name} {selectedTeacher.last_name}
              </Title>
              <Text type="secondary">{selectedTeacher.employee_id}</Text>
            </div>

            <Descriptions title="Personal Information" bordered column={1}>
              <Descriptions.Item label="Phone">{selectedTeacher.phone}</Descriptions.Item>
              <Descriptions.Item label="Email">{selectedTeacher.email || 'Not provided'}</Descriptions.Item>
              <Descriptions.Item label="Experience">{selectedTeacher.experience_years} years</Descriptions.Item>
              <Descriptions.Item label="Qualification">{selectedTeacher.qualification || 'Not specified'}</Descriptions.Item>
              <Descriptions.Item label="Specializations">
                {selectedTeacher.specializations?.split(',').map((spec, index) => (
                  <Tag key={index} color="blue" style={{ marginBottom: 4 }}>
                    {spec.trim()}
                  </Tag>
                ))}
              </Descriptions.Item>
              <Descriptions.Item label="Rating">
                <Rate disabled defaultValue={selectedTeacher.rating || 0} />
                <Text style={{ marginLeft: 8 }}>
                  {selectedTeacher.rating ? `${selectedTeacher.rating}/5` : 'No rating'}
                </Text>
              </Descriptions.Item>
              <Descriptions.Item label="Status">
                <Badge
                  status={selectedTeacher.is_active ? 'success' : 'default'}
                  text={selectedTeacher.is_active ? 'Active' : 'Inactive'}
                />
              </Descriptions.Item>
            </Descriptions>

            {selectedTeacher.bio && (
              <div style={{ marginTop: 16 }}>
                <Title level={5}>Biography</Title>
                <Text>{selectedTeacher.bio}</Text>
              </div>
            )}

            <div style={{ marginTop: 24, textAlign: 'center' }}>
              <Space>
                <Button type="primary" icon={<EditOutlined />}>
                  Edit Profile
                </Button>
                <Button icon={<CalendarOutlined />}>
                  View Schedule
                </Button>
                <Button icon={<TrophyOutlined />}>
                  Performance
                </Button>
              </Space>
            </div>
          </div>
        )}
      </Drawer>
    </div>
  )
}

export default TeachersPage
