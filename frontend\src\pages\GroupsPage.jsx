import React, { useState } from 'react'
import {
  Card,
  Table,
  Button,
  Input,
  Select,
  Space,
  Tag,
  Modal,
  Form,
  DatePicker,
  Typography,
  Row,
  Col,
  Statistic,
  Tooltip,
  message,
  Progress,
  Drawer,
  Descriptions,
  Tabs,
  TimePicker,
  Checkbox
} from 'antd'
import {
  SearchOutlined,
  UsergroupAddOutlined,
  PlusOutlined,
  EditOutlined,
  EyeOutlined,
  UserAddOutlined,
  CalendarOutlined,
  TeamOutlined,
  BookOutlined
} from '@ant-design/icons'
import { useQuery, useMutation, useQueryClient } from 'react-query'
import { useAuthStore } from '../stores/authStore'
import axios from 'axios'
import dayjs from 'dayjs'

const { Title, Text } = Typography
const { TextArea } = Input
const { TabPane } = Tabs
const { RangePicker } = DatePicker

const GroupsPage = () => {
  const { user } = useAuthStore()
  const queryClient = useQueryClient()
  const [searchText, setSearchText] = useState('')
  const [statusFilter, setStatusFilter] = useState('active')
  const [courseFilter, setCourseFilter] = useState('')
  const [selectedGroup, setSelectedGroup] = useState(null)
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [isDrawerVisible, setIsDrawerVisible] = useState(false)
  const [modalType, setModalType] = useState('') // 'create', 'edit'
  const [form] = Form.useForm()

  // Fetch groups
  const { data: groupsData, isLoading } = useQuery(
    ['groups', searchText, statusFilter, courseFilter],
    async () => {
      const params = new URLSearchParams()
      if (searchText) params.append('search', searchText)
      if (statusFilter) params.append('status', statusFilter)
      if (courseFilter) params.append('course_id', courseFilter)

      const response = await axios.get(`/groups?${params}`)
      return response.data.data
    },
    {
      refetchInterval: 30000
    }
  )

  // Fetch courses for dropdown
  const { data: coursesData } = useQuery(
    'courses',
    async () => {
      const response = await axios.get('/courses')
      return response.data.data
    }
  )

  // Fetch teachers for dropdown
  const { data: teachersData } = useQuery(
    'teachers',
    async () => {
      const response = await axios.get('/teachers')
      return response.data.data
    }
  )

  // Create group mutation
  const createGroupMutation = useMutation(
    async (data) => {
      const response = await axios.post('/groups', data)
      return response.data
    },
    {
      onSuccess: () => {
        message.success('Group created successfully')
        queryClient.invalidateQueries('groups')
        setIsModalVisible(false)
        form.resetFields()
      },
      onError: (error) => {
        message.error(error.response?.data?.message || 'Failed to create group')
      }
    }
  )

  // Update group mutation
  const updateGroupMutation = useMutation(
    async ({ groupId, data }) => {
      const response = await axios.put(`/groups/${groupId}`, data)
      return response.data
    },
    {
      onSuccess: () => {
        message.success('Group updated successfully')
        queryClient.invalidateQueries('groups')
        setIsModalVisible(false)
        form.resetFields()
      },
      onError: (error) => {
        message.error(error.response?.data?.message || 'Failed to update group')
      }
    }
  )

  const getStatusColor = (status) => {
    const colors = {
      active: 'green',
      completed: 'blue',
      cancelled: 'red'
    }
    return colors[status] || 'default'
  }

  const getCapacityColor = (current, max) => {
    const percentage = (current / max) * 100
    if (percentage >= 90) return '#ff4d4f'
    if (percentage >= 75) return '#fa8c16'
    return '#52c41a'
  }

  const handleCreateGroup = () => {
    setModalType('create')
    form.resetFields()
    setIsModalVisible(true)
  }

  const handleEditGroup = (group) => {
    setSelectedGroup(group)
    setModalType('edit')
    form.setFieldsValue({
      ...group,
      start_date: group.start_date ? dayjs(group.start_date) : null,
      end_date: group.end_date ? dayjs(group.end_date) : null,
      schedule: group.schedule || {}
    })
    setIsModalVisible(true)
  }

  const handleViewGroup = async (group) => {
    try {
      const response = await axios.get(`/groups/${group.id}`)
      setSelectedGroup(response.data.data)
      setIsDrawerVisible(true)
    } catch (error) {
      message.error('Failed to load group details')
    }
  }

  const handleModalSubmit = async (values) => {
    const formData = {
      ...values,
      start_date: values.start_date ? values.start_date.toISOString() : null,
      end_date: values.end_date ? values.end_date.toISOString() : null,
      schedule: values.schedule || {}
    }

    if (modalType === 'create') {
      await createGroupMutation.mutateAsync(formData)
    } else if (modalType === 'edit') {
      await updateGroupMutation.mutateAsync({
        groupId: selectedGroup.id,
        data: formData
      })
    }
  }

  const columns = [
    {
      title: 'Group',
      key: 'group',
      render: (_, record) => (
        <div>
          <Text strong>{record.name}</Text>
          <br />
          <Text type="secondary" style={{ fontSize: 12 }}>
            Code: {record.code}
          </Text>
        </div>
      )
    },
    {
      title: 'Course',
      key: 'course',
      render: (_, record) => (
        <div>
          <Text>{record.course_name}</Text>
          <br />
          <Tag color="blue" style={{ fontSize: 10 }}>
            {record.course_type?.replace('_', ' ')}
          </Tag>
          {record.course_level && (
            <Tag color="purple" style={{ fontSize: 10 }}>
              {record.course_level}
            </Tag>
          )}
        </div>
      )
    },
    {
      title: 'Teacher',
      key: 'teacher',
      render: (_, record) => (
        record.teacher_first_name ? (
          <div>
            <Text>{record.teacher_first_name} {record.teacher_last_name}</Text>
            <br />
            <Text type="secondary" style={{ fontSize: 12 }}>
              {record.teacher_phone}
            </Text>
          </div>
        ) : (
          <Text type="secondary">Not assigned</Text>
        )
      )
    },
    {
      title: 'Capacity',
      key: 'capacity',
      render: (_, record) => (
        <div>
          <Progress
            percent={(record.current_enrollment / record.max_capacity) * 100}
            size="small"
            strokeColor={getCapacityColor(record.current_enrollment, record.max_capacity)}
            format={() => `${record.current_enrollment}/${record.max_capacity}`}
          />
        </div>
      )
    },
    {
      title: 'Schedule',
      key: 'schedule',
      render: (_, record) => (
        <div>
          <Text style={{ fontSize: 12 }}>
            {record.room && `Room: ${record.room}`}
          </Text>
          <br />
          <Text type="secondary" style={{ fontSize: 11 }}>
            {dayjs(record.start_date).format('MMM DD')} - {dayjs(record.end_date).format('MMM DD')}
          </Text>
        </div>
      )
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={getStatusColor(status)}>
          {status?.charAt(0).toUpperCase() + status?.slice(1)}
        </Tag>
      )
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Tooltip title="View Details">
            <Button
              size="small"
              icon={<EyeOutlined />}
              onClick={() => handleViewGroup(record)}
            />
          </Tooltip>
          <Tooltip title="Edit Group">
            <Button
              size="small"
              icon={<EditOutlined />}
              onClick={() => handleEditGroup(record)}
            />
          </Tooltip>
          <Tooltip title="Add Students">
            <Button
              size="small"
              icon={<UserAddOutlined />}
              onClick={() => {
                // Student enrollment functionality
                message.info('Student enrollment feature coming soon')
              }}
            />
          </Tooltip>
        </Space>
      )
    }
  ]

  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>Group Management</Title>
        <Text type="secondary">
          Manage class groups, schedules, and student enrollments
        </Text>
      </div>

      {/* Statistics Cards */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="Total Groups"
              value={groupsData?.groups?.length || 0}
              prefix={<UsergroupAddOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Active Groups"
              value={groupsData?.groups?.filter(g => g.status === 'active').length || 0}
              prefix={<UsergroupAddOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Total Students"
              value={groupsData?.groups?.reduce((sum, g) => sum + g.current_enrollment, 0) || 0}
              prefix={<TeamOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Avg Capacity"
              value={groupsData?.groups?.length > 0 ?
                Math.round(groupsData.groups.reduce((sum, g) => sum + (g.current_enrollment / g.max_capacity * 100), 0) / groupsData.groups.length) : 0}
              prefix={<UsergroupAddOutlined />}
              suffix="%"
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Filters and Actions */}
      <Card style={{ marginBottom: 16 }}>
        <Row gutter={16} align="middle">
          <Col span={6}>
            <Input
              placeholder="Search groups..."
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              allowClear
            />
          </Col>
          <Col span={4}>
            <Select
              placeholder="Filter by status"
              value={statusFilter}
              onChange={setStatusFilter}
              style={{ width: '100%' }}
            >
              <Select.Option value="active">Active</Select.Option>
              <Select.Option value="completed">Completed</Select.Option>
              <Select.Option value="cancelled">Cancelled</Select.Option>
            </Select>
          </Col>
          <Col span={4}>
            <Select
              placeholder="Filter by course"
              value={courseFilter}
              onChange={setCourseFilter}
              allowClear
              style={{ width: '100%' }}
            >
              {coursesData?.map(course => (
                <Select.Option key={course.id} value={course.id}>
                  {course.name}
                </Select.Option>
              ))}
            </Select>
          </Col>
          <Col span={10} style={{ textAlign: 'right' }}>
            <Space>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleCreateGroup}
              >
                Create New Group
              </Button>
              <Button icon={<CalendarOutlined />}>
                Manage Schedules
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* Groups Table */}
      <Card>
        <Table
          columns={columns}
          dataSource={groupsData?.groups || []}
          loading={isLoading}
          rowKey="id"
          pagination={{
            pageSize: 20,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `Total ${total} groups`
          }}
        />
      </Card>

      {/* Create/Edit Group Modal */}
      <Modal
        title={modalType === 'create' ? 'Create New Group' : 'Edit Group'}
        open={isModalVisible}
        onCancel={() => {
          setIsModalVisible(false)
          form.resetFields()
        }}
        footer={null}
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleModalSubmit}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="Group Name"
                rules={[{ required: true, message: 'Please enter group name' }]}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="code"
                label="Group Code"
                rules={[{ required: true, message: 'Please enter group code' }]}
              >
                <Input placeholder="e.g., *********" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="course_id"
                label="Course"
                rules={[{ required: true, message: 'Please select a course' }]}
              >
                <Select placeholder="Select course">
                  {coursesData?.map(course => (
                    <Select.Option key={course.id} value={course.id}>
                      {course.name} ({course.type})
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="teacher_id"
                label="Teacher"
                rules={[{ required: true, message: 'Please select a teacher' }]}
              >
                <Select placeholder="Select teacher">
                  {teachersData?.map(teacher => (
                    <Select.Option key={teacher.id} value={teacher.id}>
                      {teacher.first_name} {teacher.last_name}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item name="room" label="Room">
                <Input placeholder="e.g., Room 101" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="max_capacity"
                label="Max Capacity"
                rules={[{ required: true, message: 'Please enter max capacity' }]}
              >
                <Input type="number" min={1} max={50} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="status" label="Status">
                <Select defaultValue="active">
                  <Select.Option value="active">Active</Select.Option>
                  <Select.Option value="completed">Completed</Select.Option>
                  <Select.Option value="cancelled">Cancelled</Select.Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="start_date"
                label="Start Date"
                rules={[{ required: true, message: 'Please select start date' }]}
              >
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="end_date"
                label="End Date"
                rules={[{ required: true, message: 'Please select end date' }]}
              >
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item label="Schedule">
            <Card size="small">
              <Text type="secondary" style={{ display: 'block', marginBottom: 16 }}>
                Select class days and times
              </Text>
              <Row gutter={16}>
                <Col span={8}>
                  <Form.Item name={['schedule', 'monday']} valuePropName="checked">
                    <Checkbox>Monday</Checkbox>
                  </Form.Item>
                  <Form.Item name={['schedule', 'tuesday']} valuePropName="checked">
                    <Checkbox>Tuesday</Checkbox>
                  </Form.Item>
                  <Form.Item name={['schedule', 'wednesday']} valuePropName="checked">
                    <Checkbox>Wednesday</Checkbox>
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item name={['schedule', 'thursday']} valuePropName="checked">
                    <Checkbox>Thursday</Checkbox>
                  </Form.Item>
                  <Form.Item name={['schedule', 'friday']} valuePropName="checked">
                    <Checkbox>Friday</Checkbox>
                  </Form.Item>
                  <Form.Item name={['schedule', 'saturday']} valuePropName="checked">
                    <Checkbox>Saturday</Checkbox>
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item name={['schedule', 'sunday']} valuePropName="checked">
                    <Checkbox>Sunday</Checkbox>
                  </Form.Item>
                  <Form.Item name={['schedule', 'start_time']} label="Start Time">
                    <TimePicker format="HH:mm" style={{ width: '100%' }} />
                  </Form.Item>
                  <Form.Item name={['schedule', 'end_time']} label="End Time">
                    <TimePicker format="HH:mm" style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
              </Row>
            </Card>
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setIsModalVisible(false)}>
                Cancel
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={createGroupMutation.isLoading || updateGroupMutation.isLoading}
              >
                {modalType === 'create' ? 'Create Group' : 'Update Group'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* Group Details Drawer */}
      <Drawer
        title="Group Details"
        placement="right"
        onClose={() => setIsDrawerVisible(false)}
        open={isDrawerVisible}
        width={700}
      >
        {selectedGroup && (
          <Tabs defaultActiveKey="1">
            <TabPane tab="Overview" key="1">
              <Descriptions column={1} bordered>
                <Descriptions.Item label="Group Name">
                  {selectedGroup.name}
                </Descriptions.Item>
                <Descriptions.Item label="Group Code">
                  {selectedGroup.code}
                </Descriptions.Item>
                <Descriptions.Item label="Course">
                  {selectedGroup.course_name} ({selectedGroup.course_type})
                </Descriptions.Item>
                <Descriptions.Item label="Teacher">
                  {selectedGroup.teacher_first_name} {selectedGroup.teacher_last_name}
                </Descriptions.Item>
                <Descriptions.Item label="Room">
                  {selectedGroup.room || 'Not assigned'}
                </Descriptions.Item>
                <Descriptions.Item label="Capacity">
                  <Progress
                    percent={(selectedGroup.current_enrollment / selectedGroup.max_capacity) * 100}
                    format={() => `${selectedGroup.current_enrollment}/${selectedGroup.max_capacity}`}
                  />
                </Descriptions.Item>
                <Descriptions.Item label="Duration">
                  {dayjs(selectedGroup.start_date).format('MMMM DD, YYYY')} - {dayjs(selectedGroup.end_date).format('MMMM DD, YYYY')}
                </Descriptions.Item>
                <Descriptions.Item label="Status">
                  <Tag color={getStatusColor(selectedGroup.status)}>
                    {selectedGroup.status?.charAt(0).toUpperCase() + selectedGroup.status?.slice(1)}
                  </Tag>
                </Descriptions.Item>
              </Descriptions>
            </TabPane>

            <TabPane tab="Students" key="2">
              {selectedGroup.students?.length > 0 ? (
                <div>
                  <div style={{ marginBottom: 16 }}>
                    <Text strong>Enrolled Students ({selectedGroup.students.length})</Text>
                  </div>
                  {selectedGroup.students.map(student => (
                    <Card key={student.id} size="small" style={{ marginBottom: 8 }}>
                      <Row justify="space-between" align="middle">
                        <Col>
                          <div>
                            <Text strong>{student.first_name} {student.last_name}</Text>
                            <Tag style={{ marginLeft: 8 }} color={student.enrollment_status === 'active' ? 'green' : 'default'}>
                              {student.enrollment_status}
                            </Tag>
                          </div>
                          <Text type="secondary">ID: {student.student_id}</Text>
                          <br />
                          <Text type="secondary" style={{ fontSize: 12 }}>
                            Level: {student.current_level || 'Not set'} | Phone: {student.phone}
                          </Text>
                        </Col>
                        <Col>
                          <Button size="small" danger>
                            Remove
                          </Button>
                        </Col>
                      </Row>
                    </Card>
                  ))}
                </div>
              ) : (
                <div style={{ textAlign: 'center', padding: '50px 0' }}>
                  <Text type="secondary">No students enrolled yet</Text>
                  <br />
                  <Button type="primary" style={{ marginTop: 16 }}>
                    Add Students
                  </Button>
                </div>
              )}
            </TabPane>

            <TabPane tab="Schedule" key="3">
              <Card>
                <Text strong>Class Schedule</Text>
                <div style={{ marginTop: 16 }}>
                  {selectedGroup.schedule && Object.keys(selectedGroup.schedule).length > 0 ? (
                    <div>
                      <Text>Days: </Text>
                      {['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']
                        .filter(day => selectedGroup.schedule[day])
                        .map(day => (
                          <Tag key={day} style={{ marginBottom: 4 }}>
                            {day.charAt(0).toUpperCase() + day.slice(1)}
                          </Tag>
                        ))}
                      <br />
                      <Text>Time: {selectedGroup.schedule.start_time} - {selectedGroup.schedule.end_time}</Text>
                    </div>
                  ) : (
                    <Text type="secondary">Schedule not configured</Text>
                  )}
                </div>
              </Card>
            </TabPane>
          </Tabs>
        )}
      </Drawer>
    </div>
  )
}

export default GroupsPage
