import React from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import { Layout } from 'antd'
import { useAuthStore } from './stores/authStore'
import LoginPage from './pages/LoginPage'
import DashboardLayout from './components/layout/DashboardLayout'
import Dashboard from './pages/Dashboard'
import LeadsPage from './pages/LeadsPage'
import StudentsPage from './pages/StudentsPage'
import GroupsPage from './pages/GroupsPage'
import TeachersPage from './pages/TeachersPage'
import CoursesPage from './pages/CoursesPage'
import TestsPage from './pages/TestsPage'
import PaymentsPage from './pages/PaymentsPage'
import CallsPage from './pages/CallsPage'
import ProfilePage from './pages/ProfilePage'
import NotFoundPage from './pages/NotFoundPage'

const { Content } = Layout

function App() {
  const { isAuthenticated, user } = useAuthStore()

  // If not authenticated, show login page
  if (!isAuthenticated) {
    return (
      <Layout style={{ minHeight: '100vh' }}>
        <Content>
          <Routes>
            <Route path="/login" element={<LoginPage />} />
            <Route path="*" element={<Navigate to="/login" replace />} />
          </Routes>
        </Content>
      </Layout>
    )
  }

  // If authenticated, show dashboard with role-based routing
  return (
    <DashboardLayout>
      <Routes>
        <Route path="/" element={<Navigate to="/dashboard" replace />} />
        <Route path="/dashboard" element={<Dashboard />} />
        <Route path="/profile" element={<ProfilePage />} />
        
        {/* Admin and Reception routes */}
        {(['admin', 'reception', 'call_center'].includes(user?.role)) && (
          <>
            <Route path="/leads" element={<LeadsPage />} />
            <Route path="/students" element={<StudentsPage />} />
          </>
        )}
        
        {/* Admin, Reception, and Teacher routes */}
        {(['admin', 'reception', 'teacher'].includes(user?.role)) && (
          <>
            <Route path="/groups" element={<GroupsPage />} />
          </>
        )}
        
        {/* Admin only routes */}
        {user?.role === 'admin' && (
          <>
            <Route path="/teachers" element={<TeachersPage />} />
            <Route path="/courses" element={<CoursesPage />} />
          </>
        )}
        
        {/* Testing Department routes */}
        {(['admin', 'testing_department', 'teacher'].includes(user?.role)) && (
          <>
            <Route path="/tests" element={<TestsPage />} />
          </>
        )}
        
        {/* Cashier and Admin routes */}
        {(['admin', 'cashier', 'reception'].includes(user?.role)) && (
          <>
            <Route path="/payments" element={<PaymentsPage />} />
          </>
        )}
        
        {/* Call Center routes */}
        {(['admin', 'call_center'].includes(user?.role)) && (
          <>
            <Route path="/calls" element={<CallsPage />} />
          </>
        )}
        
        {/* Student routes */}
        {user?.role === 'student' && (
          <>
            <Route path="/my-courses" element={<StudentsPage />} />
            <Route path="/my-tests" element={<TestsPage />} />
            <Route path="/my-payments" element={<PaymentsPage />} />
          </>
        )}
        
        {/* Parent routes */}
        {user?.role === 'parent' && (
          <>
            <Route path="/children" element={<StudentsPage />} />
            <Route path="/children-tests" element={<TestsPage />} />
            <Route path="/children-payments" element={<PaymentsPage />} />
          </>
        )}
        
        {/* 404 page */}
        <Route path="*" element={<NotFoundPage />} />
      </Routes>
    </DashboardLayout>
  )
}

export default App
