const express = require('express');
const router = express.Router();
const { query } = require('../config/database');
const { authenticate, requirePermission } = require('../middleware/auth');

// All routes require authentication
router.use(authenticate);

// Get all payments with filtering
router.get('/', requirePermission('payments:read'), async (req, res) => {
    try {
        const { status, student_id, date_from, date_to } = req.query;
        
        let whereConditions = [];
        let queryParams = [];
        let paramIndex = 1;

        if (status) {
            whereConditions.push(`p.status = $${paramIndex++}`);
            queryParams.push(status);
        }

        if (student_id) {
            whereConditions.push(`p.student_id = $${paramIndex++}`);
            queryParams.push(student_id);
        }

        if (date_from) {
            whereConditions.push(`p.created_at >= $${paramIndex++}`);
            queryParams.push(date_from);
        }

        if (date_to) {
            whereConditions.push(`p.created_at <= $${paramIndex++}`);
            queryParams.push(date_to);
        }

        const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

        const paymentsQuery = `
            SELECT 
                p.id,
                p.amount,
                p.currency,
                p.status,
                p.payment_method,
                p.due_date,
                p.paid_date,
                p.created_at,
                s.student_id,
                u.first_name as student_first_name,
                u.last_name as student_last_name,
                g.name as group_name
            FROM payments p
            JOIN students s ON p.student_id = s.id
            JOIN users u ON s.user_id = u.id
            LEFT JOIN groups g ON p.group_id = g.id
            ${whereClause}
            ORDER BY p.created_at DESC
        `;

        const result = await query(paymentsQuery, queryParams);

        res.json({
            success: true,
            data: result.rows
        });
    } catch (error) {
        console.error('Get payments error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to retrieve payments'
        });
    }
});

// Get payment by ID
router.get('/:id', requirePermission('payments:read'), async (req, res) => {
    try {
        const paymentQuery = `
            SELECT 
                p.*,
                s.student_id,
                u.first_name as student_first_name,
                u.last_name as student_last_name,
                u.phone as student_phone,
                g.name as group_name,
                cashier.first_name as cashier_first_name,
                cashier.last_name as cashier_last_name
            FROM payments p
            JOIN students s ON p.student_id = s.id
            JOIN users u ON s.user_id = u.id
            LEFT JOIN groups g ON p.group_id = g.id
            LEFT JOIN users cashier ON p.cashier_id = cashier.id
            WHERE p.id = $1
        `;

        const result = await query(paymentQuery, [req.params.id]);

        if (result.rows.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'Payment not found'
            });
        }

        res.json({
            success: true,
            data: result.rows[0]
        });
    } catch (error) {
        console.error('Get payment error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to retrieve payment'
        });
    }
});

// Placeholder for other payment endpoints
router.post('/', requirePermission('payments:create'), (req, res) => {
    res.status(501).json({
        success: false,
        message: 'Payment creation endpoint not yet implemented'
    });
});

router.put('/:id', requirePermission('payments:update'), (req, res) => {
    res.status(501).json({
        success: false,
        message: 'Payment update endpoint not yet implemented'
    });
});

module.exports = router;
