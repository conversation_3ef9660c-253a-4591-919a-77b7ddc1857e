const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const rateLimit = require('rate-limiter-flexible');
require('dotenv').config();

const app = express();

// Security middleware
app.use(helmet({
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com", "https://cdnjs.cloudflare.com"],
            fontSrc: ["'self'", "https://fonts.gstatic.com", "https://cdnjs.cloudflare.com"],
            scriptSrc: ["'self'", "https://cdnjs.cloudflare.com"],
            imgSrc: ["'self'", "data:", "https:"],
            connectSrc: ["'self'"]
        }
    }
}));

// CORS configuration
const corsOptions = {
    origin: process.env.NODE_ENV === 'production' 
        ? ['https://innovativecentre.onrender.com', 'https://innovative-centre-crm.vercel.app']
        : ['http://localhost:3000', 'http://localhost:3001'],
    credentials: true,
    optionsSuccessStatus: 200
};
app.use(cors(corsOptions));

// Compression middleware
app.use(compression());

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Rate limiting
const rateLimiter = new rateLimit.RateLimiterMemory({
    keyGenerator: (req) => req.ip,
    points: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100,
    duration: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 900, // 15 minutes
});

app.use(async (req, res, next) => {
    try {
        await rateLimiter.consume(req.ip);
        next();
    } catch (rejRes) {
        res.status(429).json({
            success: false,
            message: 'Too many requests, please try again later.',
            retryAfter: Math.round(rejRes.msBeforeNext / 1000)
        });
    }
});

// Health check endpoint
app.get('/api/health', (req, res) => {
    res.json({
        success: true,
        message: 'Innovative Centre CRM API is running',
        timestamp: new Date().toISOString(),
        version: '1.0.0'
    });
});

// Import route modules
const authRoutes = require('./routes/auth');
const leadRoutes = require('./routes/leads');
const studentRoutes = require('./routes/students');
const groupRoutes = require('./routes/groups');
const teacherRoutes = require('./routes/teachers');
const courseRoutes = require('./routes/courses');
const testRoutes = require('./routes/tests');
const paymentRoutes = require('./routes/payments');
const callRoutes = require('./routes/calls');
const dashboardRoutes = require('./routes/dashboard');

// API Routes
app.use('/api/auth', authRoutes);
app.use('/api/leads', leadRoutes);
app.use('/api/students', studentRoutes);
app.use('/api/groups', groupRoutes);
app.use('/api/teachers', teacherRoutes);
app.use('/api/courses', courseRoutes);
app.use('/api/tests', testRoutes);
app.use('/api/payments', paymentRoutes);
app.use('/api/calls', callRoutes);
app.use('/api/dashboard', dashboardRoutes);

// Landing page form integration endpoint
app.post('/api/landing/submit', async (req, res) => {
    try {
        const { name, phone, course } = req.body;
        
        // Validate required fields
        if (!name || !phone) {
            return res.status(400).json({
                success: false,
                message: 'Name and phone are required'
            });
        }

        // Process course preferences (can be array or single value)
        let coursePreferences = [];
        if (Array.isArray(course)) {
            coursePreferences = course;
        } else if (course) {
            coursePreferences = [course];
        }

        // Create lead in database
        const leadController = require('./controllers/leadController');
        const leadData = {
            name: name.trim(),
            phone: phone.trim(),
            course_preferences: coursePreferences,
            source: 'landing_page'
        };

        const result = await leadController.createLead(leadData);
        
        if (result.success) {
            // Send SMS notification to lead (optional)
            const smsService = require('./services/smsService');
            await smsService.sendWelcomeSMS(phone, name);

            res.json({
                success: true,
                message: 'Thank you! Your information has been submitted successfully. One of our education consultants will contact you shortly.',
                leadId: result.data.id
            });
        } else {
            res.status(500).json({
                success: false,
                message: 'Failed to submit your information. Please try again.'
            });
        }
    } catch (error) {
        console.error('Landing form submission error:', error);
        res.status(500).json({
            success: false,
            message: 'An error occurred while processing your request.'
        });
    }
});

// Error handling middleware
app.use((err, req, res, next) => {
    console.error('Error:', err);
    
    if (err.type === 'entity.parse.failed') {
        return res.status(400).json({
            success: false,
            message: 'Invalid JSON format'
        });
    }
    
    res.status(500).json({
        success: false,
        message: process.env.NODE_ENV === 'production' 
            ? 'Internal server error' 
            : err.message
    });
});

// 404 handler
app.use('*', (req, res) => {
    res.status(404).json({
        success: false,
        message: 'API endpoint not found'
    });
});

// For Vercel deployment
module.exports = app;

// For local development
if (require.main === module) {
    const PORT = process.env.PORT || 3000;
    app.listen(PORT, () => {
        console.log(`Innovative Centre CRM API running on port ${PORT}`);
        console.log(`Environment: ${process.env.NODE_ENV || 'development'}`);
    });
}
