const express = require('express');
const router = express.Router();
const teacherController = require('../controllers/teacherController');
const { authenticate, requirePermission } = require('../middleware/auth');

// All routes require authentication
router.use(authenticate);

// Get all teachers
router.get('/', requirePermission('teachers:read'), async (req, res) => {
    try {
        const result = await teacherController.getTeachers(req.query);

        if (result.success) {
            res.json(result);
        } else {
            res.status(400).json(result);
        }
    } catch (error) {
        console.error('Get teachers error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to retrieve teachers'
        });
    }
});

// Get teacher by ID
router.get('/:id', requirePermission('teachers:read'), async (req, res) => {
    try {
        const result = await teacherController.getTeacherById(req.params.id);

        if (result.success) {
            res.json(result);
        } else {
            res.status(404).json(result);
        }
    } catch (error) {
        console.error('Get teacher error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to retrieve teacher'
        });
    }
});

// Create new teacher
router.post('/', requirePermission('teachers:create'), async (req, res) => {
    try {
        const result = await teacherController.createTeacher(req.body);

        if (result.success) {
            res.status(201).json(result);
        } else {
            res.status(400).json(result);
        }
    } catch (error) {
        console.error('Create teacher error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to create teacher'
        });
    }
});

// Update teacher
router.put('/:id', requirePermission('teachers:update'), async (req, res) => {
    try {
        const result = await teacherController.updateTeacher(req.params.id, req.body);

        if (result.success) {
            res.json(result);
        } else {
            res.status(400).json(result);
        }
    } catch (error) {
        console.error('Update teacher error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to update teacher'
        });
    }
});

module.exports = router;
