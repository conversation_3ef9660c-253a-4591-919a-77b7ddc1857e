const express = require('express');
const router = express.Router();
const { query } = require('../config/database');
const { authenticate, requirePermission } = require('../middleware/auth');

// All routes require authentication
router.use(authenticate);

// Get all teachers
router.get('/', requirePermission('teachers:read'), async (req, res) => {
    try {
        const teachersQuery = `
            SELECT 
                t.id,
                t.employee_id,
                u.first_name,
                u.last_name,
                u.phone,
                u.email,
                t.specializations,
                t.experience_years,
                t.is_active
            FROM teachers t
            JOIN users u ON t.user_id = u.id
            ORDER BY u.first_name, u.last_name
        `;

        const result = await query(teachersQuery);

        res.json({
            success: true,
            data: result.rows
        });
    } catch (error) {
        console.error('Get teachers error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to retrieve teachers'
        });
    }
});

// Get teacher by ID
router.get('/:id', requirePermission('teachers:read'), async (req, res) => {
    try {
        const teacherQuery = `
            SELECT 
                t.*,
                u.first_name,
                u.last_name,
                u.phone,
                u.email
            FROM teachers t
            JOIN users u ON t.user_id = u.id
            WHERE t.id = $1
        `;

        const result = await query(teacherQuery, [req.params.id]);

        if (result.rows.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'Teacher not found'
            });
        }

        res.json({
            success: true,
            data: result.rows[0]
        });
    } catch (error) {
        console.error('Get teacher error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to retrieve teacher'
        });
    }
});

// Placeholder for other teacher endpoints
router.post('/', requirePermission('teachers:create'), (req, res) => {
    res.status(501).json({
        success: false,
        message: 'Teacher creation endpoint not yet implemented'
    });
});

router.put('/:id', requirePermission('teachers:update'), (req, res) => {
    res.status(501).json({
        success: false,
        message: 'Teacher update endpoint not yet implemented'
    });
});

module.exports = router;
