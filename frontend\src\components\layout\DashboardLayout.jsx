import React, { useState, useEffect } from 'react'
import { Layout, Menu, Avatar, Dropdown, Ty<PERSON><PERSON>, Button, Space, Badge } from 'antd'
import {
  DashboardOutlined,
  UserOutlined,
  TeamOutlined,
  BookOutlined,
  PhoneOutlined,
  DollarOutlined,
  FileTextOutlined,
  SettingOutlined,
  <PERSON>goutOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  BellOutlined,
  UsergroupAddOutlined,
  ExperimentOutlined,
  ContactsOutlined
} from '@ant-design/icons'
import { useNavigate, useLocation } from 'react-router-dom'
import { useAuthStore } from '../../stores/authStore'

const { Header, Sider, Content } = Layout
const { Text } = Typography

const DashboardLayout = ({ children }) => {
  const [collapsed, setCollapsed] = useState(false)
  const [isMobile, setIsMobile] = useState(false)
  const { user, logout } = useAuthStore()
  const navigate = useNavigate()
  const location = useLocation()

  // Handle responsive design
  useEffect(() => {
    const handleResize = () => {
      const mobile = window.innerWidth < 768
      setIsMobile(mobile)
      if (mobile) {
        setCollapsed(true)
      }
    }

    handleResize()
    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  // Initialize auth store
  useEffect(() => {
    const { initialize } = useAuthStore.getState()
    initialize()
  }, [])

  const handleLogout = async () => {
    await logout()
    navigate('/login')
  }

  // Generate menu items based on user role
  const getMenuItems = () => {
    const baseItems = [
      {
        key: '/dashboard',
        icon: <DashboardOutlined />,
        label: 'Dashboard'
      }
    ]

    const roleMenus = {
      admin: [
        {
          key: '/leads',
          icon: <ContactsOutlined />,
          label: 'Leads'
        },
        {
          key: '/students',
          icon: <UserOutlined />,
          label: 'Students'
        },
        {
          key: '/groups',
          icon: <UsergroupAddOutlined />,
          label: 'Groups'
        },
        {
          key: '/teachers',
          icon: <TeamOutlined />,
          label: 'Teachers'
        },
        {
          key: '/courses',
          icon: <BookOutlined />,
          label: 'Courses'
        },
        {
          key: '/tests',
          icon: <ExperimentOutlined />,
          label: 'Tests'
        },
        {
          key: '/payments',
          icon: <DollarOutlined />,
          label: 'Payments'
        },
        {
          key: '/calls',
          icon: <PhoneOutlined />,
          label: 'Call Logs'
        }
      ],
      reception: [
        {
          key: '/leads',
          icon: <ContactsOutlined />,
          label: 'Leads'
        },
        {
          key: '/students',
          icon: <UserOutlined />,
          label: 'Students'
        },
        {
          key: '/groups',
          icon: <UsergroupAddOutlined />,
          label: 'Groups'
        },
        {
          key: '/payments',
          icon: <DollarOutlined />,
          label: 'Payments'
        }
      ],
      call_center: [
        {
          key: '/leads',
          icon: <ContactsOutlined />,
          label: 'My Leads'
        },
        {
          key: '/calls',
          icon: <PhoneOutlined />,
          label: 'Call Logs'
        },
        {
          key: '/students',
          icon: <UserOutlined />,
          label: 'Students'
        }
      ],
      testing_department: [
        {
          key: '/tests',
          icon: <ExperimentOutlined />,
          label: 'Tests'
        },
        {
          key: '/students',
          icon: <UserOutlined />,
          label: 'Students'
        }
      ],
      cashier: [
        {
          key: '/payments',
          icon: <DollarOutlined />,
          label: 'Payments'
        },
        {
          key: '/students',
          icon: <UserOutlined />,
          label: 'Students'
        }
      ],
      teacher: [
        {
          key: '/groups',
          icon: <UsergroupAddOutlined />,
          label: 'My Groups'
        },
        {
          key: '/students',
          icon: <UserOutlined />,
          label: 'My Students'
        },
        {
          key: '/tests',
          icon: <ExperimentOutlined />,
          label: 'Tests'
        }
      ],
      student: [
        {
          key: '/my-courses',
          icon: <BookOutlined />,
          label: 'My Courses'
        },
        {
          key: '/my-tests',
          icon: <ExperimentOutlined />,
          label: 'My Tests'
        },
        {
          key: '/my-payments',
          icon: <DollarOutlined />,
          label: 'My Payments'
        }
      ],
      parent: [
        {
          key: '/children',
          icon: <UserOutlined />,
          label: 'My Children'
        },
        {
          key: '/children-tests',
          icon: <ExperimentOutlined />,
          label: 'Children Tests'
        },
        {
          key: '/children-payments',
          icon: <DollarOutlined />,
          label: 'Payments'
        }
      ]
    }

    return [...baseItems, ...(roleMenus[user?.role] || [])]
  }

  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: 'Profile',
      onClick: () => navigate('/profile')
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: 'Settings'
    },
    {
      type: 'divider'
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: 'Logout',
      onClick: handleLogout
    }
  ]

  const getRoleDisplayName = (role) => {
    const roleNames = {
      admin: 'Administrator',
      reception: 'Reception Staff',
      call_center: 'Call Center Agent',
      testing_department: 'Testing Department',
      cashier: 'Cashier',
      teacher: 'Teacher',
      student: 'Student',
      parent: 'Parent'
    }
    return roleNames[role] || role
  }

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sider
        trigger={null}
        collapsible
        collapsed={collapsed}
        width={250}
        style={{
          background: '#fff',
          boxShadow: '2px 0 8px rgba(0,0,0,0.1)',
          position: isMobile ? 'fixed' : 'relative',
          height: '100vh',
          zIndex: 1000
        }}
      >
        <div style={{
          height: 64,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          borderBottom: '1px solid #f0f0f0',
          background: '#0056b3'
        }}>
          {!collapsed ? (
            <Text strong style={{ color: 'white', fontSize: 16 }}>
              Innovative Centre
            </Text>
          ) : (
            <Text strong style={{ color: 'white', fontSize: 14 }}>
              IC
            </Text>
          )}
        </div>

        <Menu
          mode="inline"
          selectedKeys={[location.pathname]}
          style={{ borderRight: 0, height: 'calc(100vh - 64px)' }}
          items={getMenuItems()}
          onClick={({ key }) => navigate(key)}
        />
      </Sider>

      <Layout style={{ marginLeft: isMobile ? 0 : (collapsed ? 80 : 250) }}>
        <Header style={{
          padding: '0 24px',
          background: '#fff',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
          position: 'sticky',
          top: 0,
          zIndex: 999
        }}>
          <Space>
            <Button
              type="text"
              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              onClick={() => setCollapsed(!collapsed)}
              style={{ fontSize: 16, width: 40, height: 40 }}
            />
          </Space>

          <Space size="large">
            <Badge count={0} size="small">
              <Button
                type="text"
                icon={<BellOutlined />}
                style={{ fontSize: 16, width: 40, height: 40 }}
              />
            </Badge>

            <Dropdown
              menu={{ items: userMenuItems }}
              placement="bottomRight"
              trigger={['click']}
            >
              <Space style={{ cursor: 'pointer' }}>
                <Avatar
                  style={{ backgroundColor: '#0056b3' }}
                  icon={<UserOutlined />}
                />
                {!isMobile && (
                  <div>
                    <div style={{ fontWeight: 600 }}>
                      {user?.first_name} {user?.last_name}
                    </div>
                    <div style={{ fontSize: 12, color: '#666' }}>
                      {getRoleDisplayName(user?.role)}
                    </div>
                  </div>
                )}
              </Space>
            </Dropdown>
          </Space>
        </Header>

        <Content style={{
          margin: '24px',
          padding: '24px',
          background: '#f5f5f5',
          minHeight: 'calc(100vh - 112px)',
          borderRadius: 8
        }}>
          {children}
        </Content>
      </Layout>
    </Layout>
  )
}

export default DashboardLayout
