import React, { useState } from 'react'
import {
  Card, Typography, Button, Space, Table, Modal, Form, Input, Select,
  Tag, Tooltip, Row, Col, Statistic, Tabs, message, Drawer, Descriptions,
  InputNumber, Switch, Badge, Divider
} from 'antd'
import {
  BookOutlined, PlusOutlined, EyeOutlined, EditOutlined, DollarOutlined,
  ClockCircleOutlined, UserOutlined, TrophyOutlined, FileTextOutlined,
  SettingOutlined, TeamOutlined, CalendarOutlined
} from '@ant-design/icons'
import { useQuery, useMutation, useQueryClient } from 'react-query'
import axios from 'axios'

const { Title, Text } = Typography
const { TabPane } = Tabs
const { Option } = Select

const CoursesPage = () => {
  const [selectedCourse, setSelectedCourse] = useState(null)
  const [isCourseModalVisible, setIsCourseModalVisible] = useState(false)
  const [isCourseDrawerVisible, setIsCourseDrawerVisible] = useState(false)
  const [courseForm] = Form.useForm()
  const queryClient = useQueryClient()

  // Fetch courses data
  const { data: coursesData, isLoading: coursesLoading } = useQuery(
    'courses',
    async () => {
      const response = await axios.get('/courses')
      return response.data.data
    }
  )

  // Fetch course statistics
  const { data: courseStats } = useQuery(
    'course-stats',
    async () => {
      const response = await axios.get('/courses/statistics')
      return response.data.data
    }
  )

  // Create course mutation
  const createCourseMutation = useMutation(
    async (courseData) => {
      const response = await axios.post('/courses', courseData)
      return response.data
    },
    {
      onSuccess: () => {
        message.success('Course created successfully')
        setIsCourseModalVisible(false)
        courseForm.resetFields()
        queryClient.invalidateQueries('courses')
        queryClient.invalidateQueries('course-stats')
      },
      onError: (error) => {
        message.error(error.response?.data?.message || 'Failed to create course')
      }
    }
  )

  // Update course mutation
  const updateCourseMutation = useMutation(
    async ({ id, ...courseData }) => {
      const response = await axios.put(`/courses/${id}`, courseData)
      return response.data
    },
    {
      onSuccess: () => {
        message.success('Course updated successfully')
        setIsCourseModalVisible(false)
        courseForm.resetFields()
        queryClient.invalidateQueries('courses')
        queryClient.invalidateQueries('course-stats')
      },
      onError: (error) => {
        message.error(error.response?.data?.message || 'Failed to update course')
      }
    }
  )

  const handleCreateCourse = () => {
    setSelectedCourse(null)
    setIsCourseModalVisible(true)
    courseForm.resetFields()
  }

  const handleEditCourse = (course) => {
    setSelectedCourse(course)
    setIsCourseModalVisible(true)
    courseForm.setFieldsValue({
      ...course,
      prerequisites: course.prerequisites?.split(',') || []
    })
  }

  const handleViewCourse = (course) => {
    setSelectedCourse(course)
    setIsCourseDrawerVisible(true)
  }

  const handleCourseSubmit = async (values) => {
    const courseData = {
      ...values,
      prerequisites: values.prerequisites?.join(',') || ''
    }

    if (selectedCourse) {
      updateCourseMutation.mutate({ id: selectedCourse.id, ...courseData })
    } else {
      createCourseMutation.mutate(courseData)
    }
  }

  // Statistics data
  const statsData = [
    {
      title: 'Total Courses',
      value: coursesData?.length || 0,
      icon: <BookOutlined style={{ color: '#1890ff' }} />
    },
    {
      title: 'Active Courses',
      value: coursesData?.filter(c => c.is_active).length || 0,
      icon: <TrophyOutlined style={{ color: '#52c41a' }} />
    },
    {
      title: 'Total Students',
      value: courseStats?.total_enrolled || 0,
      icon: <UserOutlined style={{ color: '#faad14' }} />
    },
    {
      title: 'Avg Price',
      value: coursesData?.length > 0 ?
        Math.round(coursesData.reduce((sum, c) => sum + (c.price || 0), 0) / coursesData.length).toLocaleString() : 0,
      suffix: ' UZS',
      icon: <DollarOutlined style={{ color: '#ff7a45' }} />
    }
  ]

  // Courses table columns
  const coursesColumns = [
    {
      title: 'Course',
      key: 'course',
      render: (_, record) => (
        <div>
          <Text strong>{record.name}</Text>
          <br />
          <Text type="secondary" style={{ fontSize: 12 }}>
            {record.code} • {record.type}
          </Text>
        </div>
      )
    },
    {
      title: 'Level',
      dataIndex: 'level',
      key: 'level',
      render: (level) => (
        <Tag color={
          level === 'A1' || level === 'A2' ? 'green' :
          level === 'B1' || level === 'B2' ? 'blue' :
          level === 'C1' || level === 'C2' ? 'purple' : 'default'
        }>
          {level}
        </Tag>
      )
    },
    {
      title: 'Type',
      dataIndex: 'type',
      key: 'type',
      render: (type) => (
        <Tag color={
          type === 'General English' ? 'blue' :
          type === 'IELTS' ? 'gold' :
          type === 'SAT' ? 'orange' :
          type === 'Kids English' ? 'pink' :
          type === 'Math' ? 'cyan' : 'default'
        }>
          {type}
        </Tag>
      )
    },
    {
      title: 'Duration',
      key: 'duration',
      render: (_, record) => (
        <div>
          <Text>{record.duration_weeks} weeks</Text>
          <br />
          <Text type="secondary" style={{ fontSize: 12 }}>
            {record.classes_per_week}x/week • {record.class_duration_minutes}min
          </Text>
        </div>
      )
    },
    {
      title: 'Capacity',
      key: 'capacity',
      render: (_, record) => (
        <div>
          <Text>{record.min_students} - {record.max_students}</Text>
          <br />
          <Text type="secondary" style={{ fontSize: 12 }}>students</Text>
        </div>
      )
    },
    {
      title: 'Price',
      dataIndex: 'price',
      key: 'price',
      render: (price) => (
        <div>
          <Text strong style={{ color: '#52c41a' }}>
            {price?.toLocaleString()} UZS
          </Text>
        </div>
      )
    },
    {
      title: 'Status',
      dataIndex: 'is_active',
      key: 'is_active',
      render: (isActive) => (
        <Badge
          status={isActive ? 'success' : 'default'}
          text={isActive ? 'Active' : 'Inactive'}
        />
      )
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Tooltip title="View Details">
            <Button
              size="small"
              icon={<EyeOutlined />}
              onClick={() => handleViewCourse(record)}
            />
          </Tooltip>
          <Tooltip title="Edit Course">
            <Button
              size="small"
              icon={<EditOutlined />}
              onClick={() => handleEditCourse(record)}
            />
          </Tooltip>
          <Tooltip title="Manage Groups">
            <Button
              size="small"
              icon={<TeamOutlined />}
              onClick={() => {
                message.info('Group management feature coming soon')
              }}
            />
          </Tooltip>
        </Space>
      )
    }
  ]

  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>Course Management</Title>
        <Text type="secondary">
          Manage course catalog, pricing, and curriculum structure
        </Text>
      </div>

      {/* Statistics Cards */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        {statsData.map((stat, index) => (
          <Col span={6} key={index}>
            <Card>
              <Statistic
                title={stat.title}
                value={stat.value}
                suffix={stat.suffix}
                prefix={stat.icon}
                valueStyle={{ color: stat.icon.props.style.color }}
              />
            </Card>
          </Col>
        ))}
      </Row>

      {/* Action Buttons */}
      <Card style={{ marginBottom: 24 }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Space>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleCreateCourse}
              >
                Add New Course
              </Button>
              <Button icon={<DollarOutlined />}>
                Manage Pricing
              </Button>
              <Button icon={<SettingOutlined />}>
                Course Settings
              </Button>
            </Space>
          </Col>
          <Col>
            <Space>
              <Button>Export Catalog</Button>
              <Button>Import Courses</Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* Courses Table */}
      <Card>
        <Tabs defaultActiveKey="1">
          <TabPane tab="All Courses" key="1">
            <Table
              columns={coursesColumns}
              dataSource={coursesData || []}
              loading={coursesLoading}
              rowKey="id"
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `Total ${total} courses`
              }}
            />
          </TabPane>

          <TabPane tab="General English" key="2">
            <Table
              columns={coursesColumns}
              dataSource={coursesData?.filter(c => c.type === 'General English') || []}
              loading={coursesLoading}
              rowKey="id"
              pagination={{ pageSize: 10 }}
            />
          </TabPane>

          <TabPane tab="IELTS" key="3">
            <Table
              columns={coursesColumns}
              dataSource={coursesData?.filter(c => c.type === 'IELTS') || []}
              loading={coursesLoading}
              rowKey="id"
              pagination={{ pageSize: 10 }}
            />
          </TabPane>

          <TabPane tab="SAT & Math" key="4">
            <Table
              columns={coursesColumns}
              dataSource={coursesData?.filter(c => c.type === 'SAT' || c.type === 'Math') || []}
              loading={coursesLoading}
              rowKey="id"
              pagination={{ pageSize: 10 }}
            />
          </TabPane>

          <TabPane tab="Kids English" key="5">
            <Table
              columns={coursesColumns}
              dataSource={coursesData?.filter(c => c.type === 'Kids English') || []}
              loading={coursesLoading}
              rowKey="id"
              pagination={{ pageSize: 10 }}
            />
          </TabPane>
        </Tabs>
      </Card>

      {/* Create/Edit Course Modal */}
      <Modal
        title={selectedCourse ? 'Edit Course' : 'Add New Course'}
        open={isCourseModalVisible}
        onCancel={() => setIsCourseModalVisible(false)}
        footer={null}
        width={800}
      >
        <Form
          form={courseForm}
          layout="vertical"
          onFinish={handleCourseSubmit}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="Course Name"
                rules={[{ required: true, message: 'Please enter course name' }]}
              >
                <Input placeholder="e.g., General English A1" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="code"
                label="Course Code"
                rules={[{ required: true, message: 'Please enter course code' }]}
              >
                <Input placeholder="e.g., GE-A1-001" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="type"
                label="Course Type"
                rules={[{ required: true, message: 'Please select course type' }]}
              >
                <Select placeholder="Select course type">
                  <Option value="General English">General English</Option>
                  <Option value="IELTS">IELTS</Option>
                  <Option value="SAT">SAT</Option>
                  <Option value="Math">Math</Option>
                  <Option value="Kids English">Kids English</Option>
                  <Option value="Business English">Business English</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="level"
                label="Level"
                rules={[{ required: true, message: 'Please select level' }]}
              >
                <Select placeholder="Select level">
                  <Option value="A1">A1 - Beginner</Option>
                  <Option value="A2">A2 - Elementary</Option>
                  <Option value="B1">B1 - Intermediate</Option>
                  <Option value="B2">B2 - Upper Intermediate</Option>
                  <Option value="C1">C1 - Advanced</Option>
                  <Option value="C2">C2 - Proficiency</Option>
                  <Option value="Foundation">Foundation</Option>
                  <Option value="Beginner">Beginner</Option>
                  <Option value="Intermediate">Intermediate</Option>
                  <Option value="Advanced">Advanced</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="duration_weeks"
                label="Duration (weeks)"
                rules={[{ required: true, message: 'Please enter duration' }]}
              >
                <InputNumber min={1} max={52} placeholder="12" style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="classes_per_week"
                label="Classes per Week"
                rules={[{ required: true, message: 'Please enter classes per week' }]}
              >
                <InputNumber min={1} max={7} placeholder="3" style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="class_duration_minutes"
                label="Class Duration (min)"
                rules={[{ required: true, message: 'Please enter class duration' }]}
              >
                <InputNumber min={30} max={180} placeholder="90" style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="min_students"
                label="Min Students"
                rules={[{ required: true, message: 'Please enter minimum students' }]}
              >
                <InputNumber min={1} max={20} placeholder="4" style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="max_students"
                label="Max Students"
                rules={[{ required: true, message: 'Please enter maximum students' }]}
              >
                <InputNumber min={1} max={20} placeholder="12" style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="price"
                label="Price (UZS)"
                rules={[{ required: true, message: 'Please enter price' }]}
              >
                <InputNumber
                  min={0}
                  placeholder="500000"
                  style={{ width: '100%' }}
                  formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  parser={value => value.replace(/\$\s?|(,*)/g, '')}
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="prerequisites"
            label="Prerequisites"
          >
            <Select
              mode="multiple"
              placeholder="Select prerequisite courses"
              options={coursesData?.map(course => ({
                label: `${course.name} (${course.level})`,
                value: course.name
              })) || []}
            />
          </Form.Item>

          <Form.Item
            name="materials"
            label="Course Materials"
          >
            <Input.TextArea
              rows={2}
              placeholder="List of required books, materials, and resources..."
            />
          </Form.Item>

          <Form.Item
            name="description"
            label="Course Description"
          >
            <Input.TextArea
              rows={3}
              placeholder="Detailed course description, objectives, and outcomes..."
            />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="is_active"
                label="Status"
                valuePropName="checked"
                initialValue={true}
              >
                <Switch
                  checkedChildren="Active"
                  unCheckedChildren="Inactive"
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setIsCourseModalVisible(false)}>
                Cancel
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={createCourseMutation.isLoading || updateCourseMutation.isLoading}
              >
                {selectedCourse ? 'Update Course' : 'Create Course'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* Course Details Drawer */}
      <Drawer
        title="Course Details"
        placement="right"
        onClose={() => setIsCourseDrawerVisible(false)}
        open={isCourseDrawerVisible}
        width={600}
      >
        {selectedCourse && (
          <div>
            <div style={{ textAlign: 'center', marginBottom: 24 }}>
              <Title level={4}>{selectedCourse.name}</Title>
              <Space>
                <Tag color={
                  selectedCourse.level === 'A1' || selectedCourse.level === 'A2' ? 'green' :
                  selectedCourse.level === 'B1' || selectedCourse.level === 'B2' ? 'blue' :
                  selectedCourse.level === 'C1' || selectedCourse.level === 'C2' ? 'purple' : 'default'
                }>
                  {selectedCourse.level}
                </Tag>
                <Tag color={
                  selectedCourse.type === 'General English' ? 'blue' :
                  selectedCourse.type === 'IELTS' ? 'gold' :
                  selectedCourse.type === 'SAT' ? 'orange' :
                  selectedCourse.type === 'Kids English' ? 'pink' :
                  selectedCourse.type === 'Math' ? 'cyan' : 'default'
                }>
                  {selectedCourse.type}
                </Tag>
              </Space>
            </div>

            <Descriptions title="Course Information" bordered column={1}>
              <Descriptions.Item label="Course Code">{selectedCourse.code}</Descriptions.Item>
              <Descriptions.Item label="Duration">
                {selectedCourse.duration_weeks} weeks
                ({selectedCourse.classes_per_week} classes/week, {selectedCourse.class_duration_minutes} min each)
              </Descriptions.Item>
              <Descriptions.Item label="Class Capacity">
                {selectedCourse.min_students} - {selectedCourse.max_students} students
              </Descriptions.Item>
              <Descriptions.Item label="Price">
                <Text strong style={{ color: '#52c41a', fontSize: 16 }}>
                  {selectedCourse.price?.toLocaleString()} UZS
                </Text>
              </Descriptions.Item>
              <Descriptions.Item label="Prerequisites">
                {selectedCourse.prerequisites ?
                  selectedCourse.prerequisites.split(',').map((prereq, index) => (
                    <Tag key={index} style={{ marginBottom: 4 }}>
                      {prereq.trim()}
                    </Tag>
                  )) : 'None'
                }
              </Descriptions.Item>
              <Descriptions.Item label="Status">
                <Badge
                  status={selectedCourse.is_active ? 'success' : 'default'}
                  text={selectedCourse.is_active ? 'Active' : 'Inactive'}
                />
              </Descriptions.Item>
            </Descriptions>

            {selectedCourse.materials && (
              <div style={{ marginTop: 16 }}>
                <Title level={5}>Course Materials</Title>
                <Text>{selectedCourse.materials}</Text>
              </div>
            )}

            {selectedCourse.description && (
              <div style={{ marginTop: 16 }}>
                <Title level={5}>Description</Title>
                <Text>{selectedCourse.description}</Text>
              </div>
            )}

            <Divider />

            <div style={{ textAlign: 'center' }}>
              <Space>
                <Button type="primary" icon={<EditOutlined />}>
                  Edit Course
                </Button>
                <Button icon={<TeamOutlined />}>
                  View Groups
                </Button>
                <Button icon={<UserOutlined />}>
                  Enrolled Students
                </Button>
                <Button icon={<CalendarOutlined />}>
                  Schedule
                </Button>
              </Space>
            </div>
          </div>
        )}
      </Drawer>
    </div>
  )
}

export default CoursesPage
