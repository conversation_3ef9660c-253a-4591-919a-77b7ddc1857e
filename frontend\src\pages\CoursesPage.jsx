import React from 'react'
import { <PERSON>, Typo<PERSON>, Button, Space } from 'antd'
import { BookOutlined, PlusOutlined } from '@ant-design/icons'

const { Title, Text } = Typography

const CoursesPage = () => {
  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>Course Management</Title>
        <Text type="secondary">
          Manage course catalog, pricing, and curriculum
        </Text>
      </div>

      <Card>
        <div style={{ textAlign: 'center', padding: '50px 0' }}>
          <BookOutlined style={{ fontSize: 64, color: '#d9d9d9', marginBottom: 16 }} />
          <Title level={3} type="secondary">
            Course Management Interface
          </Title>
          <Text type="secondary" style={{ display: 'block', marginBottom: 24 }}>
            This page will include:
            <br />• Course catalog (General English, IELTS, SAT, Math)
            <br />• Level-based course structure (A1-C2)
            <br />• Pricing and duration management
            <br />• Prerequisites and materials
            <br />• Course capacity settings
            <br />• Curriculum and assessment methods
          </Text>
          <Space>
            <Button type="primary" icon={<PlusOutlined />}>
              Add New Course
            </Button>
            <Button>
              Manage Pricing
            </Button>
          </Space>
        </div>
      </Card>
    </div>
  )
}

export default CoursesPage
