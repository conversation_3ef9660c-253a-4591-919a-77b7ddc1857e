const express = require('express');
const router = express.Router();
const { query } = require('../config/database');
const { authenticate, requirePermission } = require('../middleware/auth');

// All routes require authentication
router.use(authenticate);

// Get all tests
router.get('/', requirePermission('tests:read'), async (req, res) => {
    try {
        const testsQuery = `
            SELECT 
                t.id,
                t.name,
                t.type,
                t.level,
                t.duration_minutes,
                t.total_score,
                t.passing_score,
                t.is_active,
                c.name as course_name
            FROM tests t
            LEFT JOIN courses c ON t.course_id = c.id
            WHERE t.is_active = true
            ORDER BY t.type, t.level
        `;

        const result = await query(testsQuery);

        res.json({
            success: true,
            data: result.rows
        });
    } catch (error) {
        console.error('Get tests error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to retrieve tests'
        });
    }
});

// Get test results for a student
router.get('/results/student/:studentId', requirePermission('test_results:read'), async (req, res) => {
    try {
        const resultsQuery = `
            SELECT 
                tr.id,
                tr.score,
                tr.percentage,
                tr.skill_breakdown,
                tr.start_time,
                tr.end_time,
                tr.passed,
                tr.certificate_url,
                t.name as test_name,
                t.type as test_type,
                t.level as test_level,
                t.total_score
            FROM test_results tr
            JOIN tests t ON tr.test_id = t.id
            WHERE tr.student_id = $1
            ORDER BY tr.start_time DESC
        `;

        const result = await query(resultsQuery, [req.params.studentId]);

        res.json({
            success: true,
            data: result.rows
        });
    } catch (error) {
        console.error('Get test results error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to retrieve test results'
        });
    }
});

// Placeholder for other test endpoints
router.post('/', requirePermission('tests:create'), (req, res) => {
    res.status(501).json({
        success: false,
        message: 'Test creation endpoint not yet implemented'
    });
});

router.post('/results', requirePermission('test_results:create'), (req, res) => {
    res.status(501).json({
        success: false,
        message: 'Test result submission endpoint not yet implemented'
    });
});

module.exports = router;
