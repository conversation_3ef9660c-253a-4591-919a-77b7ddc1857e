const express = require('express');
const router = express.Router();
const testController = require('../controllers/testController');
const { authenticate, requirePermission } = require('../middleware/auth');

// All routes require authentication
router.use(authenticate);

// Get all tests
router.get('/', requirePermission('tests:read'), async (req, res) => {
    try {
        const result = await testController.getTests(req.query);

        if (result.success) {
            res.json(result);
        } else {
            res.status(400).json(result);
        }
    } catch (error) {
        console.error('Get tests error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to retrieve tests'
        });
    }
});

// Get test by ID
router.get('/:id', requirePermission('tests:read'), async (req, res) => {
    try {
        const result = await testController.getTestById(req.params.id);

        if (result.success) {
            res.json(result);
        } else {
            res.status(404).json(result);
        }
    } catch (error) {
        console.error('Get test error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to retrieve test'
        });
    }
});

// Get test results
router.get('/results', requirePermission('test_results:read'), async (req, res) => {
    try {
        const result = await testController.getTestResults(req.query);

        if (result.success) {
            res.json(result);
        } else {
            res.status(400).json(result);
        }
    } catch (error) {
        console.error('Get test results error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to retrieve test results'
        });
    }
});

// Get test results for a student
router.get('/results/student/:studentId', requirePermission('test_results:read'), async (req, res) => {
    try {
        const resultsQuery = `
            SELECT 
                tr.id,
                tr.score,
                tr.percentage,
                tr.skill_breakdown,
                tr.start_time,
                tr.end_time,
                tr.passed,
                tr.certificate_url,
                t.name as test_name,
                t.type as test_type,
                t.level as test_level,
                t.total_score
            FROM test_results tr
            JOIN tests t ON tr.test_id = t.id
            WHERE tr.student_id = $1
            ORDER BY tr.start_time DESC
        `;

        const result = await query(resultsQuery, [req.params.studentId]);

        res.json({
            success: true,
            data: result.rows
        });
    } catch (error) {
        console.error('Get test results error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to retrieve test results'
        });
    }
});

// Create new test
router.post('/', requirePermission('tests:create'), async (req, res) => {
    try {
        const result = await testController.createTest(req.body);

        if (result.success) {
            res.status(201).json(result);
        } else {
            res.status(400).json(result);
        }
    } catch (error) {
        console.error('Create test error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to create test'
        });
    }
});

// Schedule test for student
router.post('/schedule', requirePermission('test_results:create'), async (req, res) => {
    try {
        const result = await testController.scheduleTest(req.body);

        if (result.success) {
            res.status(201).json(result);
        } else {
            res.status(400).json(result);
        }
    } catch (error) {
        console.error('Schedule test error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to schedule test'
        });
    }
});

module.exports = router;
