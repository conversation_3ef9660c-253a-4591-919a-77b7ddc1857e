@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f5f5;
}

#root {
  min-height: 100vh;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Ant Design customizations */
.ant-layout {
  min-height: 100vh;
}

.ant-layout-sider {
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
}

.ant-menu-item-selected {
  background-color: #e6f7ff !important;
}

.ant-table-thead > tr > th {
  background-color: #fafafa;
  font-weight: 600;
}

.ant-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.ant-btn-primary {
  background-color: #0056b3;
  border-color: #0056b3;
}

.ant-btn-primary:hover {
  background-color: #004494;
  border-color: #004494;
}

/* Loading spinner */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

/* Dashboard cards */
.dashboard-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.dashboard-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.stat-card {
  text-align: center;
  padding: 24px;
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  color: #0056b3;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 1rem;
  color: #666;
  font-weight: 500;
}

/* Form styles */
.form-container {
  max-width: 600px;
  margin: 0 auto;
  padding: 24px;
}

.form-header {
  text-align: center;
  margin-bottom: 32px;
}

.form-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.form-subtitle {
  color: #666;
  font-size: 0.9rem;
}

/* Status badges */
.status-badge {
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
}

.status-new {
  background-color: #e6f7ff;
  color: #1890ff;
}

.status-contacted {
  background-color: #fff7e6;
  color: #fa8c16;
}

.status-interested {
  background-color: #f6ffed;
  color: #52c41a;
}

.status-enrolled {
  background-color: #f6ffed;
  color: #389e0d;
}

.status-lost {
  background-color: #fff2f0;
  color: #cf1322;
}

.status-active {
  background-color: #f6ffed;
  color: #52c41a;
}

.status-inactive {
  background-color: #f5f5f5;
  color: #8c8c8c;
}

.status-pending {
  background-color: #fff7e6;
  color: #fa8c16;
}

.status-paid {
  background-color: #f6ffed;
  color: #52c41a;
}

.status-overdue {
  background-color: #fff2f0;
  color: #cf1322;
}

/* Responsive design */
@media (max-width: 768px) {
  .ant-layout-sider {
    position: fixed !important;
    height: 100vh;
    z-index: 1000;
  }
  
  .ant-layout-content {
    margin-left: 0 !important;
  }
  
  .form-container {
    padding: 16px;
  }
  
  .stat-number {
    font-size: 2rem;
  }
}
