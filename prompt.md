# Innovative Centre CRM System - Development Prompt

## IMPLEMENTATION STATUS: PHASE 2 IN PROGRESS
**Phase 1**: Core CRM Foundation - ✅ **COMPLETED** (December 2024)
**Phase 2**: Advanced Features - 🚧 **IN PROGRESS** (Started December 2024)
**Target Completion**: Phase 2 by February 2025

### Phase 1 Completion Summary (December 2024)
**Status**: ✅ COMPLETED - All core infrastructure implemented and functional

**Major Achievements:**
- ✅ Complete PostgreSQL database schema with all required tables
- ✅ Robust Express.js API with JWT authentication and role-based permissions
- ✅ Fully functional lead management system with SMS integration
- ✅ Landing page integration working (form submissions save to CRM database)
- ✅ Dashboard statistics endpoints for all user roles
- ✅ SMS service integration with Uzbek providers (Eskiz.uz, MyTelecom.uz)
- ✅ React frontend foundation with Ant Design UI framework
- ✅ Authentication state management and role-based routing
- ✅ Security middleware (rate limiting, CORS, input validation)
- ✅ Basic API endpoints for all modules (students, groups, teachers, courses, tests, payments, calls)

**Current System Capabilities:**
- Lead capture from landing page works end-to-end
- Authentication system with 8 user roles fully functional
- Lead management with filtering, pagination, and assignment
- Automatic SMS notifications to new leads
- Role-based dashboard statistics
- Secure API with proper error handling and validation

**Technical Foundation:**
- Database: PostgreSQL with comprehensive schema
- Backend: Node.js/Express with Vercel optimization
- Frontend: React + Ant Design + Zustand state management
- Authentication: JWT with role-based permissions
- SMS: Integration with Uzbek providers
- Security: Rate limiting, CORS, helmet, input validation

## Project Overview
Build a comprehensive Customer Relationship Management (CRM) system for Innovative Centre, an English language center in Uzbekistan. The system should integrate with the existing landing page at https://innovativecentre.onrender.com/ and enhance the current basic class management system. The landing page exists in the projecet already as a index.html

## Business Context
- **Institution**: Innovative Centre - English language center
- **Student Base**: 4,000+ students across 2 branches
- **Course Types**: General English, IELTS, SAT, Math (primarily English focus)
- **Age Groups**: Kids to adults
- **Class Formats**: Group (12-30 students), Individual, Online
- **Languages**: English/Uzbek interface support
- **Currency**: UZS (Uzbek Som)

## Current System Reference
**Existing CRM System Analysis:**
- Reference images provided in `/example of the CRM/` directory show current crude CRM system
- **Groups functionality** is the primary feature to maintain and enhance
- Current system shows basic student management and group organization
- New system should preserve familiar Groups workflow while adding advanced features
- UI/UX should be modernized but maintain intuitive group management structure

## Core System Requirements

### 1. Call Centre & Sales Process Management
**Call Centre Functionality:**
- Integrated VoIP system with call recording capabilities
- Automatic call logging and conversation transcription
- Call quality monitoring and evaluation
- Lead assignment and distribution to call centre agents
- Real-time call metrics and performance tracking
- Call outcome tracking (interested, not interested, callback, enrolled)
- Follow-up scheduling and reminder system
- Call script management and guidance
- Integration with CRM for seamless data flow

**Sales Process Tracking:**
- Lead source attribution and tracking
- Conversion funnel analytics (Lead → Call → Trial → Enrollment)
- Sales agent performance metrics
- New student assignment tracking
- Trial class booking and attendance rates
- Enrollment conversion rates by agent/source
- Revenue attribution per sales activity
- Sales pipeline management with stages
- Automated follow-up sequences

**Reception Integration:**
- Unified dashboard for reception staff
- Walk-in lead capture and immediate assignment
- Appointment scheduling and management
- Visitor log and tracking
- Integration with call centre for seamless handoffs

### 2. Lead Management & Landing Page Integration
**Integration with existing index.html form:**
- Capture leads from existing 3-field contact form (name, phone, course preference)
- Create API endpoints for seamless data flow from current form structure
- Track lead sources and conversion rates
- Implement lead scoring based on engagement
- Maintain existing form design and user experience

**Lead Capture Fields (from existing index.html form):**
- Full name
- Phone number (Uzbek format: +998XXXXXXXXX) - PRIMARY CONTACT METHOD
- Course preference (which course the student wants to take)

**Simplified Lead Workflow:**
1. Lead capture from existing landing page form (name, phone, course preference)
2. Automatic SMS notification to lead
3. Lead assignment to call centre agents via dashboard
4. Call centre contact and follow-up
5. Placement test scheduling via Testing Department
6. Level assignment based on test results
7. Course enrollment and group assignment
8. Conversion tracking throughout process

### 2. Student Management System
**Enhanced Student Profiles:**
- Personal Information
  - Full name (English/Cyrillic)
  - Phone number
  - Emergency contact
  - Photo upload

- Academic Information
  - Initial English level assessment via placement test
  - Placement test results with detailed skill breakdown
  - Current level status (A1, A2, B1, B2, C1, C2)
  - Level progression history and test scores
  - Learning goals and objectives
  - Previous English learning experience
  - Preferred learning style
  - Special needs/accommodations
  - Level-up test eligibility and scheduling

- Progress Tracking
  - Current course enrollment
  - Historical course completion
  - Assessment scores and progress
  - Attendance records
  - Homework completion rates
  - Teacher feedback and notes
  - Certificates earned

**Student Lifecycle Management:**
- Lead (name, phone, course preference) → Call Centre Contact → Placement Test → Level Assignment → Course Enrollment
- Course progression tracking with level tests
- Level progression: A1 → A2 → B1 → B2 → C1 → C2
- Level-up tests before advancing to next course
- Re-enrollment campaigns for continuing students
- Graduation and certification
- Alumni management and advanced course offerings

### 3. Groups & Class Management (Enhanced from Current System)
**Groups Functionality (Maintaining Current System Structure):**
- Group creation and management (similar to current CRM)
- Student assignment to groups with capacity limits
- Group-based scheduling and timetables
- Group performance tracking and analytics
- Bulk operations for group management
- Group communication and announcements
- Attendance tracking by group
- Group-based payment and billing

**Course Catalog:**
- General English (A1, A2, B1, B2, C1, C2)
- IELTS Preparation (Target bands: 5.5, 6.0, 6.5, 7.0+)
- SAT Preparation
- Math courses
- Kids English (by age groups)
- Business English
- Conversation clubs

**Class Scheduling System:**
- Advanced scheduling with conflict detection
- Teacher availability management
- Room/cabinet booking system
- Capacity management (12-30 students per class)
- Substitute teacher assignment
- Holiday and break scheduling
- Make-up class scheduling

**Class Details:**
- Class codes and naming conventions
- Duration and intensity
- Pricing structure
- Prerequisites
- Materials and textbooks required
- Assessment methods

### 4. Teacher Management
**Teacher Profiles:**
- Personal and contact information
- Qualifications and certifications
- Teaching experience and specializations
- Language proficiency
- Availability and schedule preferences
- Performance metrics
- Salary and payment information

**Teacher Performance Tracking:**
- Student satisfaction ratings
- Class attendance rates
- Student progress in their classes
- Professional development activities
- Peer evaluations

### 5. Payment & Financial Management
**Payment Processing:**
- Multiple payment methods (Cash, UzCard, Humo, Payme, Click)
- Installment plans and payment schedules
- Automatic SMS payment reminders
- Late payment tracking and penalties
- Refund processing
- Family/group discounts
- QR code payments (popular in Uzbekistan)

**Financial Reporting:**
- Revenue tracking by course type
- Payment status dashboard
- Outstanding balances
- Refund reports
- Teacher payment tracking
- Monthly/quarterly financial summaries

### 6. Communication System
**Dashboard-Based Communications (No Email Dependency):**
- In-app notification system for all users
- SMS notifications for critical updates (class changes, payments)
- WhatsApp Business integration for informal communication
- Push notifications for mobile webapp
- Parent dashboard with real-time updates
- Student portal with announcements and updates

**Communication Channels:**
- Primary: Webapp dashboards with real-time notifications
- SMS notifications (local Uzbek providers: Eskiz.uz, MyTelecom.uz)
- WhatsApp Business integration
- In-app messaging system between teachers/students/parents
- Telegram Bot integration (popular in Uzbekistan)

**Parent Portal Features:**
- Real-time access to child's progress
- Class schedules and updates
- Payment status and history
- Direct messaging with teachers
- Homework assignments and submissions
- Attendance tracking
- Certificate downloads
- Event and announcement notifications

### 7. Assessment & Testing System
**Placement Test System:**
- **Initial Level Assessment**: Comprehensive placement test for new students
- **Multi-skill Testing**: Reading, Writing, Listening, Speaking components
- **Automated Scoring**: Instant results with detailed breakdown
- **Level Determination**: Automatic assignment to appropriate course level (A1-C2)
- **Test Bank Management**: Large question pool with randomization
- **Adaptive Testing**: Questions adjust based on student performance
- **Test History**: Complete record of all placement tests taken
- **Retake Policy**: Configurable retake intervals and conditions
- **Certificate Generation**: Official placement test certificates

**Level Progression Tests:**
- **Level-Up Assessments**: Tests to progress from one level to another (A1→A2, A2→B1, etc.)
- **Prerequisites Checking**: Ensure student readiness for next level
- **Comprehensive Evaluation**: All four skills assessment for level progression
- **Pass/Fail Criteria**: Configurable passing scores for each level transition
- **Progress Tracking**: Visual progress indicators and level completion status
- **Automatic Enrollment**: Upon passing, automatic enrollment in next level course
- **Remedial Recommendations**: Suggestions for areas needing improvement
- **Level Certificates**: Official certificates for completed levels

**Ongoing Assessment System:**
- Monthly progress assessments
- Mid-term and final exams
- IELTS mock tests with scoring
- Continuous assessment tracking
- Portfolio management
- Skill-specific mini-tests
- Speaking assessment recordings
- Writing sample evaluations

**Test Administration:**
- **Online Testing Platform**: Web-based testing interface
- **Proctoring Options**: Live or recorded proctoring for important tests
- **Time Management**: Automatic timing and submission
- **Question Types**: Multiple choice, fill-in-blanks, essay, speaking recordings
- **Accessibility Features**: Support for students with special needs
- **Mobile Compatibility**: Tests accessible on tablets and smartphones

**Reporting & Analytics:**
- Individual student progress reports
- Class performance summaries
- Teacher effectiveness reports
- Course completion analytics
- Certification tracking
- Level progression analytics
- Test performance trends
- Comparative analysis across groups

### 8. Facility Management
**Cabinet/Room Management:**
- Room capacity and equipment tracking
- Booking and scheduling system
- Maintenance requests
- Equipment inventory
- Availability calendar

### 9. Analytics & Reporting Dashboard
**Key Performance Indicators:**
- Student enrollment trends
- Course popularity and profitability
- Teacher performance metrics
- Lead conversion rates
- Student retention rates
- Revenue per student
- Class utilization rates

**Advanced Analytics:**
- Predictive analytics for student success
- Churn prediction and prevention
- Optimal class size analysis
- Revenue forecasting
- Marketing campaign effectiveness

## Technical Requirements

### Backend Architecture
- **Framework**: Node.js with Express.js (Vercel-optimized)
- **Database**: PostgreSQL (Vercel-compatible)
Connection string:

postgresql://crm_owner:<EMAIL>/crm?sslmode=require

- **Authentication**: JWT-based authentication with role-based access
- **API Design**: RESTful APIs with comprehensive documentation
- **File Storage**: Vercel Blob or Cloudinary for documents and images
- **SMS Service**: Integration with local Uzbek SMS providers (Eskiz.uz, MyTelecom.uz)
- **Real-time**: WebSocket or Server-Sent Events for live updates
- **Deployment**: Vercel serverless functions

### Frontend Requirements
- **Framework**: React.js or Vue.js
- **UI Library**: Modern component library (Ant Design, Material-UI, or Tailwind CSS)
- **Responsive Design**: Mobile-first approach
- **Multilingual**: English/Uzbek language support
- **Data Visualization**: Charts and graphs for analytics
- **Real-time Updates**: WebSocket integration for live updates

### Integration Requirements
- **Landing Page Integration**: Seamless API integration with existing Vercel-hosted site
- **Call Centre Integration**: VoIP providers (Twilio, Asterisk, or local Uzbek providers)
- **Call Recording**: Cloud-based call recording and storage
- **Payment Gateways**: UzCard, Humo, Payme, Click (Uzbek payment systems)
- **Calendar Integration**: Google Calendar, custom calendar system
- **Video Conferencing**: Zoom integration for online classes
- **Export Capabilities**: PDF reports, Excel exports
- **Backup System**: Automated database backups
- **SMS Providers**: Eskiz.uz, MyTelecom.uz integration
- **Messaging Apps**: WhatsApp Business API, Telegram Bot API
- **Analytics Tools**: Call analytics and conversation intelligence

### Security & Compliance
- **Data Protection**: GDPR-compliant data handling
- **Access Control**: Role-based permissions (Admin, Manager, Teacher, Student)
- **Audit Logging**: Track all system changes
- **Data Encryption**: Encrypt sensitive information
- **Session Management**: Secure session handling

## User Roles & Permissions

### Administrator
- Full system access and configuration
- User management across all roles
- System-wide reporting and analytics
- Database management and backups
- Security settings and audit logs
- Integration management
- Financial oversight and reporting

### Reception Staff
- Lead capture and initial processing
- Walk-in visitor management
- Appointment scheduling
- Basic student information updates
- Payment collection and recording
- Trial class booking
- Phone call routing and basic inquiries
- **Group Management**: Add students to groups directly (for walk-in enrollments)
- **Student Enrollment**: Process in-person student registrations and group assignments

### Testing Department
- **Test Administration**: Manage all placement tests and level progression tests
- **Test Scheduling**: Schedule placement tests for new students and level-up tests
- **Test Monitoring**: Proctor online and in-person tests
- **Scoring Management**: Review and validate automated test scores
- **Level Assignment**: Determine appropriate course levels based on test results
- **Test Bank Management**: Create, update, and maintain question pools
- **Assessment Analytics**: Generate test performance reports and statistics
- **Student Level Tracking**: Monitor student progression through levels
- **Test Certification**: Issue official test certificates and level completion documents
- **Remedial Recommendations**: Provide guidance for students needing additional support
- **Test Policy Management**: Set passing criteria and retake policies
- **Quality Assurance**: Ensure test integrity and standardization

### Call Centre Agents
- Lead management and follow-up (name, phone, course preference)
- Call recording and logging
- Sales pipeline management
- Placement test scheduling coordination with Testing Department
- Student enrollment processing after level assignment
- Performance metrics tracking
- Call script access and guidance
- Lead qualification and course consultation

### Cashier
- Payment processing and recording
- Financial transaction management
- Receipt generation and printing
- Payment plan setup and monitoring
- Refund processing
- Daily cash reconciliation
- Payment reminder management

### Teachers
- Class management and attendance
- Student progress updates and assessments
- Grade recording and feedback
- Homework assignment and tracking
- Parent/student communication
- Schedule viewing and updates
- Resource and material access
- **Test Administration**: Conduct placement tests and level-up assessments
- **Test Monitoring**: Proctor online tests and speaking assessments
- **Level Recommendations**: Recommend students for level progression tests
- **Assessment Creation**: Create and manage custom assessments

### Students
- Personal dashboard (accessible via mobile webapp)
- Schedule viewing with real-time updates
- Progress tracking and certificates
- Payment history and status
- Direct messaging with teachers
- Homework submissions and materials
- Attendance history
- Push notifications for important updates
- **Test Taking**: Access to placement tests and level progression tests
- **Test Results**: View detailed test results and skill breakdowns
- **Level Status**: Current level display and progression tracking
- **Test Scheduling**: Book level-up tests when eligible

### Parents (for kids' classes)
- Comprehensive child monitoring dashboard
- Real-time progress and attendance updates
- Direct communication with teachers via webapp
- Payment management and history
- Schedule updates and notifications
- Homework tracking and submission status
- Certificate and achievement downloads
- Photo/video sharing from classes (with permission)
- Event calendar and important dates

## Data Migration & Setup
- Import existing student data from current system
- Set up initial course catalog
- Configure payment methods and pricing
- Establish user roles and permissions
- Create initial teacher and staff accounts

## Performance Requirements
**FAST and LIGHT CRM System:**
- Sub-second response times for all operations
- Lightweight UI with minimal loading times
- Optimized database queries and indexing
- Efficient caching strategies
- Minimal resource consumption

**Scalability Requirements:**
- Support 4,000+ concurrent users
- Horizontal scaling capabilities
- Auto-scaling based on demand
- Load balancing for high availability
- Database sharding for large datasets

**Reliability & Performance:**
- 99.9% uptime requirement
- Real-time data synchronization
- Offline capability for critical functions
- Progressive web app (PWA) features
- Mobile-first responsive design

## Success Metrics
**Call Centre & Sales Effectiveness:**
- Reduce lead response time by 80%
- Increase call-to-enrollment conversion by 35%
- Track and improve new student assignment rates
- Achieve 90% call recording and logging accuracy
- Reduce sales cycle time by 50%

**Overall System Performance:**
- Increase student retention by 25%
- Improve teacher efficiency by 40%
- Automate 70% of routine communications
- Achieve 95% payment collection rate
- Maintain system response times under 1 second

## Development Phases

### Phase 1: Core CRM with Call Centre ✅ **COMPLETED** (December 2024)
**Duration**: 4 weeks (Accelerated from planned 10-12 weeks)
**Status**: All objectives achieved and functional

**Completed Features:**
- ✅ User authentication and role-based access (8 user types including Testing Department)
- ✅ Student management foundation and Groups functionality structure
- ✅ Call centre integration framework (ready for VoIP integration)
- ✅ Basic course/class management endpoints
- ✅ Payment tracking and cashier functions framework
- ✅ Landing page integration with existing index.html form (name, phone, course preference) - **FULLY FUNCTIONAL**
- ✅ Reception dashboard statistics and group management capabilities
- ✅ Basic Placement Test System structure: Initial level assessment framework
- ✅ Testing Department Dashboard: Basic test administration interface structure
- ✅ SMS notification system with Uzbek providers
- ✅ Comprehensive database schema with all required tables
- ✅ Security middleware and API rate limiting
- ✅ Lead management system with filtering and assignment

### Phase 2: Advanced Features 🚧 **IN PROGRESS** (Started December 2024)
**Duration**: 6-8 weeks (Optimized timeline)
**Current Focus**: Frontend UI Development and Advanced Testing System

**Priority Tasks (In Order):**
1. **Frontend UI Development** (Weeks 1-3)
   - Login page and dashboard layout
   - Lead management interface for call center agents
   - Student management interface for reception staff
   - Testing Department interface for test administration
   - Group management interface with student enrollment capabilities

2. **Complete Testing System** (Weeks 2-4)
   - Placement tests and level progression tests implementation
   - Test bank management, scoring, and certification
   - Level management: A1-C2 progression tracking and automation
   - Assessment system and ongoing evaluations

3. **Enhanced Features** (Weeks 4-6)
   - Communication automation and advanced notifications
   - Advanced reporting and analytics dashboards
   - Teacher management interface
   - Mobile optimization and responsive design
   - Sales pipeline and tracking with simplified lead workflow

4. **Integration & Polish** (Weeks 6-8)
   - VoIP call center integration
   - Payment gateway integration (UzCard, Humo, Payme, Click)
   - File upload system for student photos and documents
   - Real-time notifications with WebSocket

### Phase 3: Analytics & Optimization (4-6 weeks)
**Status**: Planned for February 2025
- Advanced analytics dashboard with predictive features
- Call centre performance metrics and conversation intelligence
- Performance optimization and caching strategies
- Integration expansions and third-party services
- Scalability enhancements for 4,000+ concurrent users
- Comprehensive testing and quality assurance

## Deployment Requirements (Vercel-Optimized)
- **Hosting**: Vercel with serverless functions
- **Database**: MongoDB Atlas (recommended) or PlanetScale MySQL
- **File Storage**: Vercel Blob or Cloudinary
- **CDN**: Vercel's built-in CDN
- **Environment Variables**: Secure configuration via Vercel
- **Monitoring**: Vercel Analytics + custom logging
- **Backup**: Automated database backups to cloud storage
- **Domain**: Custom domain configuration on Vercel

## Vercel-Specific Implementation Notes
- Use Vercel's serverless functions for API routes
- Implement edge functions for real-time features
- Optimize for Vercel's deployment pipeline
- Use Vercel's environment variables for sensitive data
- Implement proper caching strategies for performance
- Use Vercel's preview deployments for testing

## Documentation & Training
- Complete API documentation
- User manuals for each role
- Video tutorials
- Training sessions for staff
- System administration guide

## Support & Maintenance
- Bug fixing and updates
- Feature enhancements
- Performance monitoring
- User support system
- Regular security updates

---

**Note**: This system should seamlessly integrate with the existing landing page and enhance the current basic functionality shown in the screenshots, providing a comprehensive solution for managing the 4,000+ students across both branches of Innovative Centre.