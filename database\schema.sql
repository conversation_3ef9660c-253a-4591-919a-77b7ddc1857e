-- Innovative Centre CRM Database Schema
-- PostgreSQL Database Schema for Comprehensive CRM System

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- User Roles Enum
CREATE TYPE user_role AS ENUM (
    'admin',
    'reception',
    'testing_department',
    'call_center',
    'cashier',
    'teacher',
    'student',
    'parent'
);

-- Course Types Enum
CREATE TYPE course_type AS ENUM (
    'general_english',
    'ielts',
    'sat',
    'math',
    'kids_english',
    'business_english',
    'conversation_club'
);

-- English Levels Enum
CREATE TYPE english_level AS ENUM (
    'A1',
    'A2',
    'B1',
    'B2',
    'C1',
    'C2'
);

-- Lead Status Enum
CREATE TYPE lead_status AS ENUM (
    'new',
    'contacted',
    'interested',
    'not_interested',
    'callback_scheduled',
    'test_scheduled',
    'enrolled',
    'lost'
);

-- Payment Status Enum
CREATE TYPE payment_status AS ENUM (
    'pending',
    'paid',
    'partial',
    'overdue',
    'refunded'
);

-- Test Types Enum
CREATE TYPE test_type AS ENUM (
    'placement',
    'level_progression',
    'mock_ielts',
    'final_exam',
    'monthly_assessment'
);

-- Users Table (All system users)
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE,
    phone VARCHAR(20) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role user_role NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    last_login TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Leads Table (From landing page form)
CREATE TABLE leads (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(200) NOT NULL,
    phone VARCHAR(20) NOT NULL,
    course_preferences TEXT[], -- Array of course preferences
    status lead_status DEFAULT 'new',
    source VARCHAR(100) DEFAULT 'landing_page',
    assigned_agent_id UUID REFERENCES users(id),
    notes TEXT,
    follow_up_date TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Students Table (Detailed student profiles)
CREATE TABLE students (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    lead_id UUID REFERENCES leads(id),
    student_id VARCHAR(20) UNIQUE NOT NULL, -- Custom student ID
    date_of_birth DATE,
    gender VARCHAR(10),
    address TEXT,
    emergency_contact_name VARCHAR(200),
    emergency_contact_phone VARCHAR(20),
    photo_url VARCHAR(500),
    current_level english_level,
    initial_level english_level,
    learning_goals TEXT,
    previous_experience TEXT,
    special_needs TEXT,
    parent_id UUID REFERENCES users(id), -- For kids classes
    enrollment_date DATE DEFAULT CURRENT_DATE,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Teachers Table
CREATE TABLE teachers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    employee_id VARCHAR(20) UNIQUE NOT NULL,
    qualifications TEXT[],
    specializations course_type[],
    experience_years INTEGER,
    hourly_rate DECIMAL(10,2),
    availability_schedule JSONB, -- Store weekly schedule
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Courses Table
CREATE TABLE courses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(200) NOT NULL,
    code VARCHAR(20) UNIQUE NOT NULL,
    type course_type NOT NULL,
    level english_level,
    description TEXT,
    duration_weeks INTEGER,
    classes_per_week INTEGER,
    class_duration_minutes INTEGER,
    max_students INTEGER DEFAULT 30,
    min_students INTEGER DEFAULT 12,
    price DECIMAL(10,2),
    materials TEXT[],
    prerequisites TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Groups Table (Class groups - maintaining current system structure)
CREATE TABLE groups (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(200) NOT NULL,
    code VARCHAR(20) UNIQUE NOT NULL,
    course_id UUID REFERENCES courses(id),
    teacher_id UUID REFERENCES teachers(id),
    room VARCHAR(50),
    schedule JSONB, -- Store class schedule
    start_date DATE,
    end_date DATE,
    max_capacity INTEGER DEFAULT 30,
    current_enrollment INTEGER DEFAULT 0,
    status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Group Enrollments (Many-to-many relationship)
CREATE TABLE group_enrollments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    student_id UUID REFERENCES students(id),
    group_id UUID REFERENCES groups(id),
    enrollment_date DATE DEFAULT CURRENT_DATE,
    completion_date DATE,
    status VARCHAR(20) DEFAULT 'active',
    final_grade VARCHAR(5),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(student_id, group_id)
);

-- Tests Table
CREATE TABLE tests (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(200) NOT NULL,
    type test_type NOT NULL,
    course_id UUID REFERENCES courses(id),
    level english_level,
    duration_minutes INTEGER,
    total_score INTEGER,
    passing_score INTEGER,
    questions JSONB, -- Store test questions
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Test Results Table
CREATE TABLE test_results (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    student_id UUID REFERENCES students(id),
    test_id UUID REFERENCES tests(id),
    score INTEGER,
    percentage DECIMAL(5,2),
    skill_breakdown JSONB, -- Reading, Writing, Listening, Speaking scores
    answers JSONB, -- Store student answers
    start_time TIMESTAMP,
    end_time TIMESTAMP,
    proctor_id UUID REFERENCES users(id),
    passed BOOLEAN,
    certificate_url VARCHAR(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Payments Table
CREATE TABLE payments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    student_id UUID REFERENCES students(id),
    group_id UUID REFERENCES groups(id),
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'UZS',
    status payment_status DEFAULT 'pending',
    payment_method VARCHAR(50),
    transaction_id VARCHAR(100),
    due_date DATE,
    paid_date DATE,
    cashier_id UUID REFERENCES users(id),
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Call Logs Table
CREATE TABLE call_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    lead_id UUID REFERENCES leads(id),
    agent_id UUID REFERENCES users(id),
    phone_number VARCHAR(20),
    call_type VARCHAR(20), -- 'inbound', 'outbound'
    duration_seconds INTEGER,
    outcome VARCHAR(50),
    recording_url VARCHAR(500),
    transcript TEXT,
    notes TEXT,
    follow_up_required BOOLEAN DEFAULT false,
    follow_up_date TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Attendance Table
CREATE TABLE attendance (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    student_id UUID REFERENCES students(id),
    group_id UUID REFERENCES groups(id),
    class_date DATE NOT NULL,
    status VARCHAR(20) DEFAULT 'present', -- 'present', 'absent', 'late'
    notes TEXT,
    marked_by UUID REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(student_id, group_id, class_date)
);

-- Create indexes for better performance
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_phone ON users(phone);
CREATE INDEX idx_leads_status ON leads(status);
CREATE INDEX idx_leads_phone ON leads(phone);
CREATE INDEX idx_students_current_level ON students(current_level);
CREATE INDEX idx_students_user_id ON students(user_id);
CREATE INDEX idx_groups_teacher_id ON groups(teacher_id);
CREATE INDEX idx_groups_course_id ON groups(course_id);
CREATE INDEX idx_payments_student_id ON payments(student_id);
CREATE INDEX idx_payments_status ON payments(status);
CREATE INDEX idx_test_results_student_id ON test_results(student_id);
CREATE INDEX idx_call_logs_lead_id ON call_logs(lead_id);
CREATE INDEX idx_attendance_student_group ON attendance(student_id, group_id);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply updated_at triggers to relevant tables
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_leads_updated_at BEFORE UPDATE ON leads FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_students_updated_at BEFORE UPDATE ON students FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_teachers_updated_at BEFORE UPDATE ON teachers FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_courses_updated_at BEFORE UPDATE ON courses FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_groups_updated_at BEFORE UPDATE ON groups FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_payments_updated_at BEFORE UPDATE ON payments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
