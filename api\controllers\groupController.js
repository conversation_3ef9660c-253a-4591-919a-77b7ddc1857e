const { query, transaction } = require('../config/database');
const Joi = require('joi');

// Validation schemas
const createGroupSchema = Joi.object({
    name: Joi.string().min(2).max(200).required(),
    code: Joi.string().min(2).max(20).required(),
    course_id: Joi.string().uuid().required(),
    teacher_id: Joi.string().uuid().required(),
    room: Joi.string().max(50).optional(),
    schedule: Joi.object().required(), // JSON object for schedule
    start_date: Joi.date().required(),
    end_date: Joi.date().required(),
    max_capacity: Joi.number().integer().min(1).max(50).default(30)
});

const updateGroupSchema = Joi.object({
    name: Joi.string().min(2).max(200).optional(),
    teacher_id: Joi.string().uuid().optional(),
    room: Joi.string().max(50).allow('').optional(),
    schedule: Joi.object().optional(),
    start_date: Joi.date().optional(),
    end_date: Joi.date().optional(),
    max_capacity: Joi.number().integer().min(1).max(50).optional(),
    status: Joi.string().valid('active', 'completed', 'cancelled').optional()
});

// Generate unique group code
const generateGroupCode = async (courseId) => {
    // Get course code
    const courseResult = await query(
        'SELECT code FROM courses WHERE id = $1',
        [courseId]
    );

    if (courseResult.rows.length === 0) {
        throw new Error('Course not found');
    }

    const courseCode = courseResult.rows[0].code;
    const year = new Date().getFullYear().toString().slice(-2);
    const month = (new Date().getMonth() + 1).toString().padStart(2, '0');

    // Get the last group code for this course and month
    const lastGroup = await query(
        `SELECT code FROM groups 
         WHERE code LIKE $1 
         ORDER BY code DESC LIMIT 1`,
        [`${courseCode}${year}${month}%`]
    );

    let sequence = 1;
    if (lastGroup.rows.length > 0) {
        const lastCode = lastGroup.rows[0].code;
        const lastSequence = parseInt(lastCode.slice(-2));
        sequence = lastSequence + 1;
    }

    return `${courseCode}${year}${month}${sequence.toString().padStart(2, '0')}`;
};

// Create new group
const createGroup = async (groupData) => {
    try {
        // Validate input data
        const { error, value } = createGroupSchema.validate(groupData);
        if (error) {
            return {
                success: false,
                message: error.details[0].message
            };
        }

        const {
            name, course_id, teacher_id, room, schedule,
            start_date, end_date, max_capacity
        } = value;

        // Check if course exists
        const courseCheck = await query(
            'SELECT id, name FROM courses WHERE id = $1 AND is_active = true',
            [course_id]
        );

        if (courseCheck.rows.length === 0) {
            return {
                success: false,
                message: 'Course not found or inactive'
            };
        }

        // Check if teacher exists and is available
        const teacherCheck = await query(
            'SELECT id FROM teachers WHERE id = $1 AND is_active = true',
            [teacher_id]
        );

        if (teacherCheck.rows.length === 0) {
            return {
                success: false,
                message: 'Teacher not found or inactive'
            };
        }

        // Generate group code
        const code = value.code || await generateGroupCode(course_id);

        // Check if group code already exists
        const codeCheck = await query(
            'SELECT id FROM groups WHERE code = $1',
            [code]
        );

        if (codeCheck.rows.length > 0) {
            return {
                success: false,
                message: 'Group code already exists'
            };
        }

        // Create group
        const result = await query(
            `INSERT INTO groups (
                name, code, course_id, teacher_id, room, schedule,
                start_date, end_date, max_capacity, current_enrollment, status
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, 0, 'active')
             RETURNING *`,
            [name, code, course_id, teacher_id, room, JSON.stringify(schedule), start_date, end_date, max_capacity]
        );

        return {
            success: true,
            message: 'Group created successfully',
            data: result.rows[0]
        };
    } catch (error) {
        console.error('Error creating group:', error);
        return {
            success: false,
            message: 'Failed to create group'
        };
    }
};

// Get all groups with filtering and pagination
const getGroups = async (filters = {}, pagination = {}) => {
    try {
        const {
            search,
            teacher_id,
            course_id,
            status = 'active',
            start_date_from,
            start_date_to
        } = filters;

        const {
            page = 1,
            limit = 20,
            sort_by = 'created_at',
            sort_order = 'DESC'
        } = pagination;

        let whereConditions = ['g.status = $1'];
        let queryParams = [status];
        let paramIndex = 2;

        // Build WHERE conditions
        if (search) {
            whereConditions.push(`(g.name ILIKE $${paramIndex} OR g.code ILIKE $${paramIndex})`);
            queryParams.push(`%${search}%`);
            paramIndex++;
        }

        if (teacher_id) {
            whereConditions.push(`g.teacher_id = $${paramIndex++}`);
            queryParams.push(teacher_id);
        }

        if (course_id) {
            whereConditions.push(`g.course_id = $${paramIndex++}`);
            queryParams.push(course_id);
        }

        if (start_date_from) {
            whereConditions.push(`g.start_date >= $${paramIndex++}`);
            queryParams.push(start_date_from);
        }

        if (start_date_to) {
            whereConditions.push(`g.start_date <= $${paramIndex++}`);
            queryParams.push(start_date_to);
        }

        const whereClause = `WHERE ${whereConditions.join(' AND ')}`;

        // Count total records
        const countQuery = `
            SELECT COUNT(*) as total
            FROM groups g
            ${whereClause}
        `;
        const countResult = await query(countQuery, queryParams);
        const total = parseInt(countResult.rows[0].total);

        // Calculate pagination
        const offset = (page - 1) * limit;
        const totalPages = Math.ceil(total / limit);

        // Get groups with related information
        const groupsQuery = `
            SELECT 
                g.id,
                g.name,
                g.code,
                g.room,
                g.schedule,
                g.start_date,
                g.end_date,
                g.max_capacity,
                g.current_enrollment,
                g.status,
                g.created_at,
                c.name as course_name,
                c.type as course_type,
                c.level as course_level,
                c.price as course_price,
                t.id as teacher_id,
                u.first_name as teacher_first_name,
                u.last_name as teacher_last_name,
                u.phone as teacher_phone
            FROM groups g
            LEFT JOIN courses c ON g.course_id = c.id
            LEFT JOIN teachers t ON g.teacher_id = t.id
            LEFT JOIN users u ON t.user_id = u.id
            ${whereClause}
            ORDER BY g.${sort_by} ${sort_order}
            LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
        `;

        queryParams.push(limit, offset);
        const groupsResult = await query(groupsQuery, queryParams);

        return {
            success: true,
            data: {
                groups: groupsResult.rows,
                pagination: {
                    current_page: page,
                    total_pages: totalPages,
                    total_records: total,
                    records_per_page: limit,
                    has_next: page < totalPages,
                    has_prev: page > 1
                }
            }
        };
    } catch (error) {
        console.error('Error getting groups:', error);
        return {
            success: false,
            message: 'Failed to retrieve groups'
        };
    }
};

// Get group by ID with students
const getGroupById = async (groupId) => {
    try {
        // Get group information
        const groupQuery = `
            SELECT 
                g.*,
                c.name as course_name,
                c.type as course_type,
                c.level as course_level,
                c.description as course_description,
                c.price as course_price,
                c.duration_weeks,
                c.classes_per_week,
                c.class_duration_minutes,
                t.id as teacher_id,
                u.first_name as teacher_first_name,
                u.last_name as teacher_last_name,
                u.phone as teacher_phone,
                u.email as teacher_email
            FROM groups g
            LEFT JOIN courses c ON g.course_id = c.id
            LEFT JOIN teachers t ON g.teacher_id = t.id
            LEFT JOIN users u ON t.user_id = u.id
            WHERE g.id = $1
        `;

        const groupResult = await query(groupQuery, [groupId]);

        if (groupResult.rows.length === 0) {
            return {
                success: false,
                message: 'Group not found'
            };
        }

        // Get enrolled students
        const studentsQuery = `
            SELECT 
                s.id,
                s.student_id,
                s.current_level,
                s.enrollment_date as student_enrollment_date,
                u.first_name,
                u.last_name,
                u.phone,
                u.email,
                ge.enrollment_date as group_enrollment_date,
                ge.status as enrollment_status,
                ge.final_grade
            FROM group_enrollments ge
            JOIN students s ON ge.student_id = s.id
            JOIN users u ON s.user_id = u.id
            WHERE ge.group_id = $1
            ORDER BY ge.enrollment_date DESC
        `;

        const studentsResult = await query(studentsQuery, [groupId]);

        const groupData = groupResult.rows[0];
        groupData.students = studentsResult.rows;

        return {
            success: true,
            data: groupData
        };
    } catch (error) {
        console.error('Error getting group by ID:', error);
        return {
            success: false,
            message: 'Failed to retrieve group'
        };
    }
};

// Update group
const updateGroup = async (groupId, updateData) => {
    try {
        // Validate input data
        const { error, value } = updateGroupSchema.validate(updateData);
        if (error) {
            return {
                success: false,
                message: error.details[0].message
            };
        }

        // Build dynamic update query
        const updateFields = [];
        const queryParams = [];
        let paramIndex = 1;

        Object.keys(value).forEach(key => {
            if (value[key] !== undefined) {
                if (key === 'schedule') {
                    updateFields.push(`${key} = $${paramIndex++}`);
                    queryParams.push(JSON.stringify(value[key]));
                } else {
                    updateFields.push(`${key} = $${paramIndex++}`);
                    queryParams.push(value[key]);
                }
            }
        });

        if (updateFields.length === 0) {
            return {
                success: false,
                message: 'No fields to update'
            };
        }

        updateFields.push(`updated_at = CURRENT_TIMESTAMP`);
        queryParams.push(groupId);

        const updateQuery = `
            UPDATE groups 
            SET ${updateFields.join(', ')}
            WHERE id = $${paramIndex}
            RETURNING *
        `;

        const result = await query(updateQuery, queryParams);

        if (result.rows.length === 0) {
            return {
                success: false,
                message: 'Group not found'
            };
        }

        return {
            success: true,
            message: 'Group updated successfully',
            data: result.rows[0]
        };
    } catch (error) {
        console.error('Error updating group:', error);
        return {
            success: false,
            message: 'Failed to update group'
        };
    }
};

// Add student to group
const addStudentToGroup = async (groupId, studentId) => {
    try {
        // Check if group exists and has capacity
        const groupCheck = await query(
            'SELECT max_capacity, current_enrollment, status FROM groups WHERE id = $1',
            [groupId]
        );

        if (groupCheck.rows.length === 0) {
            return {
                success: false,
                message: 'Group not found'
            };
        }

        const group = groupCheck.rows[0];
        if (group.status !== 'active') {
            return {
                success: false,
                message: 'Cannot enroll in inactive group'
            };
        }

        if (group.current_enrollment >= group.max_capacity) {
            return {
                success: false,
                message: 'Group is at maximum capacity'
            };
        }

        // Check if student exists
        const studentCheck = await query(
            'SELECT id FROM students WHERE id = $1 AND is_active = true',
            [studentId]
        );

        if (studentCheck.rows.length === 0) {
            return {
                success: false,
                message: 'Student not found or inactive'
            };
        }

        // Check if student is already enrolled
        const enrollmentCheck = await query(
            'SELECT id FROM group_enrollments WHERE student_id = $1 AND group_id = $2 AND status = $3',
            [studentId, groupId, 'active']
        );

        if (enrollmentCheck.rows.length > 0) {
            return {
                success: false,
                message: 'Student is already enrolled in this group'
            };
        }

        // Add student to group
        await transaction(async (client) => {
            await client.query(
                'INSERT INTO group_enrollments (student_id, group_id, status) VALUES ($1, $2, $3)',
                [studentId, groupId, 'active']
            );

            await client.query(
                'UPDATE groups SET current_enrollment = current_enrollment + 1 WHERE id = $1',
                [groupId]
            );
        });

        return {
            success: true,
            message: 'Student added to group successfully'
        };
    } catch (error) {
        console.error('Error adding student to group:', error);
        return {
            success: false,
            message: 'Failed to add student to group'
        };
    }
};

// Remove student from group
const removeStudentFromGroup = async (groupId, studentId) => {
    try {
        // Check if enrollment exists
        const enrollmentCheck = await query(
            'SELECT id FROM group_enrollments WHERE student_id = $1 AND group_id = $2 AND status = $3',
            [studentId, groupId, 'active']
        );

        if (enrollmentCheck.rows.length === 0) {
            return {
                success: false,
                message: 'Student is not enrolled in this group'
            };
        }

        // Remove student from group
        await transaction(async (client) => {
            await client.query(
                'UPDATE group_enrollments SET status = $1 WHERE student_id = $2 AND group_id = $3 AND status = $4',
                ['withdrawn', studentId, groupId, 'active']
            );

            await client.query(
                'UPDATE groups SET current_enrollment = current_enrollment - 1 WHERE id = $1',
                [groupId]
            );
        });

        return {
            success: true,
            message: 'Student removed from group successfully'
        };
    } catch (error) {
        console.error('Error removing student from group:', error);
        return {
            success: false,
            message: 'Failed to remove student from group'
        };
    }
};

module.exports = {
    createGroup,
    getGroups,
    getGroupById,
    updateGroup,
    addStudentToGroup,
    removeStudentFromGroup
};
