const express = require('express');
const router = express.Router();
const groupController = require('../controllers/groupController');
const { authenticate, requirePermission } = require('../middleware/auth');

// All routes require authentication
router.use(authenticate);

// Get all groups with filtering and pagination
router.get('/', requirePermission('groups:read'), async (req, res) => {
    try {
        const filters = {
            search: req.query.search,
            teacher_id: req.query.teacher_id,
            course_id: req.query.course_id,
            status: req.query.status || 'active',
            start_date_from: req.query.start_date_from,
            start_date_to: req.query.start_date_to
        };

        const pagination = {
            page: parseInt(req.query.page) || 1,
            limit: parseInt(req.query.limit) || 20,
            sort_by: req.query.sort_by || 'created_at',
            sort_order: req.query.sort_order || 'DESC'
        };

        const result = await groupController.getGroups(filters, pagination);

        if (result.success) {
            res.json(result);
        } else {
            res.status(500).json(result);
        }
    } catch (error) {
        console.error('Get groups error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to retrieve groups'
        });
    }
});

// Get group by ID with students
router.get('/:id', requirePermission('groups:read'), async (req, res) => {
    try {
        const result = await groupController.getGroupById(req.params.id);

        if (result.success) {
            res.json(result);
        } else {
            res.status(404).json(result);
        }
    } catch (error) {
        console.error('Get group error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to retrieve group'
        });
    }
});

// Create new group
router.post('/', requirePermission('groups:create'), async (req, res) => {
    try {
        const result = await groupController.createGroup(req.body);

        if (result.success) {
            res.status(201).json(result);
        } else {
            res.status(400).json(result);
        }
    } catch (error) {
        console.error('Create group error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to create group'
        });
    }
});

// Update group
router.put('/:id', requirePermission('groups:update'), async (req, res) => {
    try {
        const result = await groupController.updateGroup(req.params.id, req.body);

        if (result.success) {
            res.json(result);
        } else {
            res.status(400).json(result);
        }
    } catch (error) {
        console.error('Update group error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to update group'
        });
    }
});

// Add student to group
router.post('/:id/students', requirePermission('groups:update'), async (req, res) => {
    try {
        const { student_id } = req.body;

        if (!student_id) {
            return res.status(400).json({
                success: false,
                message: 'Student ID is required'
            });
        }

        const result = await groupController.addStudentToGroup(req.params.id, student_id);

        if (result.success) {
            res.json(result);
        } else {
            res.status(400).json(result);
        }
    } catch (error) {
        console.error('Add student to group error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to add student to group'
        });
    }
});

// Remove student from group
router.delete('/:id/students/:studentId', requirePermission('groups:update'), async (req, res) => {
    try {
        const result = await groupController.removeStudentFromGroup(req.params.id, req.params.studentId);

        if (result.success) {
            res.json(result);
        } else {
            res.status(400).json(result);
        }
    } catch (error) {
        console.error('Remove student from group error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to remove student from group'
        });
    }
});

module.exports = router;
