const express = require('express');
const bcrypt = require('bcryptjs');
const Joi = require('joi');
const router = express.Router();
const { query } = require('../config/database');
const { generateToken, authenticate } = require('../middleware/auth');

// Validation schemas
const loginSchema = Joi.object({
    phone: Joi.string().required(),
    password: Joi.string().required()
});

const registerSchema = Joi.object({
    email: Joi.string().email().optional(),
    phone: Joi.string().pattern(/^\+?[1-9]\d{1,14}$/).required(),
    password: Joi.string().min(6).required(),
    first_name: Joi.string().min(2).max(100).required(),
    last_name: Joi.string().min(2).max(100).required(),
    role: Joi.string().valid('admin', 'reception', 'testing_department', 'call_center', 'cashier', 'teacher', 'student', 'parent').required()
});

const changePasswordSchema = Joi.object({
    current_password: Joi.string().required(),
    new_password: Joi.string().min(6).required()
});

// Login endpoint
router.post('/login', async (req, res) => {
    try {
        // Validate input
        const { error, value } = loginSchema.validate(req.body);
        if (error) {
            return res.status(400).json({
                success: false,
                message: error.details[0].message
            });
        }

        const { phone, password } = value;

        // Find user by phone
        const userResult = await query(
            'SELECT id, email, phone, password_hash, role, first_name, last_name, is_active FROM users WHERE phone = $1',
            [phone]
        );

        if (userResult.rows.length === 0) {
            return res.status(401).json({
                success: false,
                message: 'Invalid phone number or password'
            });
        }

        const user = userResult.rows[0];

        // Check if account is active
        if (!user.is_active) {
            return res.status(401).json({
                success: false,
                message: 'Account is deactivated. Please contact administrator.'
            });
        }

        // Verify password
        const isPasswordValid = await bcrypt.compare(password, user.password_hash);
        if (!isPasswordValid) {
            return res.status(401).json({
                success: false,
                message: 'Invalid phone number or password'
            });
        }

        // Update last login
        await query(
            'UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = $1',
            [user.id]
        );

        // Generate JWT token
        const token = generateToken({
            userId: user.id,
            role: user.role,
            phone: user.phone
        });

        // Remove sensitive data
        delete user.password_hash;

        res.json({
            success: true,
            message: 'Login successful',
            data: {
                user,
                token,
                expires_in: process.env.JWT_EXPIRES_IN || '7d'
            }
        });
    } catch (error) {
        console.error('Login error:', error);
        res.status(500).json({
            success: false,
            message: 'Login failed'
        });
    }
});

// Register endpoint (admin only)
router.post('/register', authenticate, async (req, res) => {
    try {
        // Only admins can create new users
        if (req.user.role !== 'admin') {
            return res.status(403).json({
                success: false,
                message: 'Only administrators can create new users'
            });
        }

        // Validate input
        const { error, value } = registerSchema.validate(req.body);
        if (error) {
            return res.status(400).json({
                success: false,
                message: error.details[0].message
            });
        }

        const { email, phone, password, first_name, last_name, role } = value;

        // Check if user already exists
        const existingUser = await query(
            'SELECT id FROM users WHERE phone = $1 OR ($2 IS NOT NULL AND email = $2)',
            [phone, email]
        );

        if (existingUser.rows.length > 0) {
            return res.status(400).json({
                success: false,
                message: 'User with this phone number or email already exists'
            });
        }

        // Hash password
        const saltRounds = parseInt(process.env.BCRYPT_ROUNDS) || 12;
        const password_hash = await bcrypt.hash(password, saltRounds);

        // Create user
        const result = await query(
            `INSERT INTO users (email, phone, password_hash, first_name, last_name, role)
             VALUES ($1, $2, $3, $4, $5, $6)
             RETURNING id, email, phone, first_name, last_name, role, is_active, created_at`,
            [email, phone, password_hash, first_name, last_name, role]
        );

        res.status(201).json({
            success: true,
            message: 'User created successfully',
            data: result.rows[0]
        });
    } catch (error) {
        console.error('Registration error:', error);
        res.status(500).json({
            success: false,
            message: 'Registration failed'
        });
    }
});

// Get current user profile
router.get('/profile', authenticate, async (req, res) => {
    try {
        const userResult = await query(
            `SELECT 
                id, email, phone, first_name, last_name, role, 
                is_active, last_login, created_at
             FROM users 
             WHERE id = $1`,
            [req.user.id]
        );

        if (userResult.rows.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }

        res.json({
            success: true,
            data: userResult.rows[0]
        });
    } catch (error) {
        console.error('Get profile error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to retrieve profile'
        });
    }
});

// Update user profile
router.put('/profile', authenticate, async (req, res) => {
    try {
        const allowedFields = ['email', 'first_name', 'last_name'];
        const updateData = {};
        
        // Only allow updating specific fields
        allowedFields.forEach(field => {
            if (req.body[field] !== undefined) {
                updateData[field] = req.body[field];
            }
        });

        if (Object.keys(updateData).length === 0) {
            return res.status(400).json({
                success: false,
                message: 'No valid fields to update'
            });
        }

        // Build dynamic update query
        const updateFields = [];
        const queryParams = [];
        let paramIndex = 1;

        Object.keys(updateData).forEach(key => {
            updateFields.push(`${key} = $${paramIndex++}`);
            queryParams.push(updateData[key]);
        });

        updateFields.push(`updated_at = CURRENT_TIMESTAMP`);
        queryParams.push(req.user.id);

        const updateQuery = `
            UPDATE users 
            SET ${updateFields.join(', ')}
            WHERE id = $${paramIndex}
            RETURNING id, email, phone, first_name, last_name, role, is_active, updated_at
        `;

        const result = await query(updateQuery, queryParams);

        res.json({
            success: true,
            message: 'Profile updated successfully',
            data: result.rows[0]
        });
    } catch (error) {
        console.error('Update profile error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to update profile'
        });
    }
});

// Change password
router.post('/change-password', authenticate, async (req, res) => {
    try {
        // Validate input
        const { error, value } = changePasswordSchema.validate(req.body);
        if (error) {
            return res.status(400).json({
                success: false,
                message: error.details[0].message
            });
        }

        const { current_password, new_password } = value;

        // Get current password hash
        const userResult = await query(
            'SELECT password_hash FROM users WHERE id = $1',
            [req.user.id]
        );

        if (userResult.rows.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }

        // Verify current password
        const isCurrentPasswordValid = await bcrypt.compare(current_password, userResult.rows[0].password_hash);
        if (!isCurrentPasswordValid) {
            return res.status(400).json({
                success: false,
                message: 'Current password is incorrect'
            });
        }

        // Hash new password
        const saltRounds = parseInt(process.env.BCRYPT_ROUNDS) || 12;
        const new_password_hash = await bcrypt.hash(new_password, saltRounds);

        // Update password
        await query(
            'UPDATE users SET password_hash = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2',
            [new_password_hash, req.user.id]
        );

        res.json({
            success: true,
            message: 'Password changed successfully'
        });
    } catch (error) {
        console.error('Change password error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to change password'
        });
    }
});

// Logout endpoint (client-side token removal)
router.post('/logout', authenticate, (req, res) => {
    res.json({
        success: true,
        message: 'Logged out successfully'
    });
});

// Verify token endpoint
router.get('/verify', authenticate, (req, res) => {
    res.json({
        success: true,
        message: 'Token is valid',
        data: {
            user: req.user
        }
    });
});

// Get all users (admin only)
router.get('/users', authenticate, async (req, res) => {
    try {
        if (req.user.role !== 'admin') {
            return res.status(403).json({
                success: false,
                message: 'Access denied'
            });
        }

        const { role, is_active, search } = req.query;
        
        let whereConditions = [];
        let queryParams = [];
        let paramIndex = 1;

        if (role) {
            whereConditions.push(`role = $${paramIndex++}`);
            queryParams.push(role);
        }

        if (is_active !== undefined) {
            whereConditions.push(`is_active = $${paramIndex++}`);
            queryParams.push(is_active === 'true');
        }

        if (search) {
            whereConditions.push(`(first_name ILIKE $${paramIndex} OR last_name ILIKE $${paramIndex} OR phone ILIKE $${paramIndex})`);
            queryParams.push(`%${search}%`);
            paramIndex++;
        }

        const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

        const usersQuery = `
            SELECT 
                id, email, phone, first_name, last_name, role, 
                is_active, last_login, created_at, updated_at
            FROM users
            ${whereClause}
            ORDER BY created_at DESC
        `;

        const result = await query(usersQuery, queryParams);

        res.json({
            success: true,
            data: result.rows
        });
    } catch (error) {
        console.error('Get users error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to retrieve users'
        });
    }
});

module.exports = router;
