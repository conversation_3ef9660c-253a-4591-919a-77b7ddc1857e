import React, { useState } from 'react'
import {
  Card, Typo<PERSON>, Button, Space, Table, Modal, Form, Input, Select,
  DatePicker, TimePicker, Tag, Tooltip, Row, Col, Statistic, Tabs,
  message, Drawer, Descriptions, Progress, Badge
} from 'antd'
import {
  ExperimentOutlined, PlusOutlined, EyeOutlined, EditOutlined,
  CalendarOutlined, ClockCircleOutlined, UserOutlined, BookOutlined,
  TrophyOutlined, Bar<PERSON>hartOutlined, FileTextOutlined, CheckCircleOutlined
} from '@ant-design/icons'
import { useQuery, useMutation, useQueryClient } from 'react-query'
import axios from 'axios'
import dayjs from 'dayjs'

const { Title, Text } = Typography
const { TabPane } = Tabs
const { Option } = Select

const TestsPage = () => {
  const [selectedTest, setSelectedTest] = useState(null)
  const [isTestModalVisible, setIsTestModalVisible] = useState(false)
  const [isScheduleModalVisible, setIsScheduleModalVisible] = useState(false)
  const [isResultsDrawerVisible, setIsResultsDrawerVisible] = useState(false)
  const [testForm] = Form.useForm()
  const [scheduleForm] = Form.useForm()
  const queryClient = useQueryClient()

  // Fetch tests data
  const { data: testsData, isLoading: testsLoading } = useQuery(
    'tests',
    async () => {
      const response = await axios.get('/tests')
      return response.data.data
    }
  )

  // Fetch test results
  const { data: resultsData, isLoading: resultsLoading } = useQuery(
    'test-results',
    async () => {
      const response = await axios.get('/tests/results')
      return response.data.data
    }
  )

  // Fetch students for test scheduling
  const { data: studentsData } = useQuery(
    'students-for-tests',
    async () => {
      const response = await axios.get('/students?status=active')
      return response.data.data
    }
  )

  // Create test mutation
  const createTestMutation = useMutation(
    async (testData) => {
      const response = await axios.post('/tests', testData)
      return response.data
    },
    {
      onSuccess: () => {
        message.success('Test created successfully')
        setIsTestModalVisible(false)
        testForm.resetFields()
        queryClient.invalidateQueries('tests')
      },
      onError: (error) => {
        message.error(error.response?.data?.message || 'Failed to create test')
      }
    }
  )

  // Schedule test mutation
  const scheduleTestMutation = useMutation(
    async (scheduleData) => {
      const response = await axios.post('/tests/schedule', scheduleData)
      return response.data
    },
    {
      onSuccess: () => {
        message.success('Test scheduled successfully')
        setIsScheduleModalVisible(false)
        scheduleForm.resetFields()
        queryClient.invalidateQueries('test-results')
      },
      onError: (error) => {
        message.error(error.response?.data?.message || 'Failed to schedule test')
      }
    }
  )

  const handleCreateTest = () => {
    setIsTestModalVisible(true)
  }

  const handleScheduleTest = () => {
    setIsScheduleModalVisible(true)
  }

  const handleViewResults = (test) => {
    setSelectedTest(test)
    setIsResultsDrawerVisible(true)
  }

  const handleTestSubmit = async (values) => {
    createTestMutation.mutate(values)
  }

  const handleScheduleSubmit = async (values) => {
    const scheduleData = {
      ...values,
      test_date: values.test_date.format('YYYY-MM-DD'),
      test_time: values.test_time.format('HH:mm')
    }
    scheduleTestMutation.mutate(scheduleData)
  }

  // Statistics cards data
  const statsData = [
    {
      title: 'Total Tests',
      value: testsData?.length || 0,
      icon: <ExperimentOutlined style={{ color: '#1890ff' }} />
    },
    {
      title: 'Scheduled Today',
      value: resultsData?.filter(r => dayjs(r.test_date).isSame(dayjs(), 'day')).length || 0,
      icon: <CalendarOutlined style={{ color: '#52c41a' }} />
    },
    {
      title: 'Pending Results',
      value: resultsData?.filter(r => r.status === 'scheduled').length || 0,
      icon: <ClockCircleOutlined style={{ color: '#faad14' }} />
    },
    {
      title: 'Completed This Month',
      value: resultsData?.filter(r =>
        r.status === 'completed' &&
        dayjs(r.test_date).isSame(dayjs(), 'month')
      ).length || 0,
      icon: <CheckCircleOutlined style={{ color: '#52c41a' }} />
    }
  ]

  // Tests table columns
  const testsColumns = [
    {
      title: 'Test Name',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <div>
          <Text strong>{text}</Text>
          <br />
          <Text type="secondary" style={{ fontSize: 12 }}>
            {record.type} • {record.level}
          </Text>
        </div>
      )
    },
    {
      title: 'Type',
      dataIndex: 'type',
      key: 'type',
      render: (type) => (
        <Tag color={
          type === 'placement' ? 'blue' :
          type === 'level_progression' ? 'green' :
          type === 'assessment' ? 'orange' : 'default'
        }>
          {type.replace('_', ' ').toUpperCase()}
        </Tag>
      )
    },
    {
      title: 'Level',
      dataIndex: 'level',
      key: 'level',
      render: (level) => (
        <Tag color="purple">{level}</Tag>
      )
    },
    {
      title: 'Duration',
      dataIndex: 'duration_minutes',
      key: 'duration_minutes',
      render: (minutes) => `${minutes} min`
    },
    {
      title: 'Total Score',
      dataIndex: 'total_score',
      key: 'total_score'
    },
    {
      title: 'Passing Score',
      dataIndex: 'passing_score',
      key: 'passing_score',
      render: (score, record) => (
        <div>
          <Text>{score}</Text>
          <Text type="secondary"> / {record.total_score}</Text>
        </div>
      )
    },
    {
      title: 'Status',
      dataIndex: 'is_active',
      key: 'is_active',
      render: (isActive) => (
        <Badge
          status={isActive ? 'success' : 'default'}
          text={isActive ? 'Active' : 'Inactive'}
        />
      )
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Tooltip title="View Details">
            <Button
              size="small"
              icon={<EyeOutlined />}
              onClick={() => handleViewResults(record)}
            />
          </Tooltip>
          <Tooltip title="Edit Test">
            <Button
              size="small"
              icon={<EditOutlined />}
              onClick={() => {
                // Edit test functionality
                message.info('Edit test feature coming soon')
              }}
            />
          </Tooltip>
          <Tooltip title="Schedule Test">
            <Button
              size="small"
              icon={<CalendarOutlined />}
              onClick={() => {
                setSelectedTest(record)
                setIsScheduleModalVisible(true)
              }}
            />
          </Tooltip>
        </Space>
      )
    }
  ]

  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>Testing Department</Title>
        <Text type="secondary">
          Manage placement tests, level progression tests, and student assessments
        </Text>
      </div>

      {/* Statistics Cards */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        {statsData.map((stat, index) => (
          <Col span={6} key={index}>
            <Card>
              <Statistic
                title={stat.title}
                value={stat.value}
                prefix={stat.icon}
                valueStyle={{ color: stat.icon.props.style.color }}
              />
            </Card>
          </Col>
        ))}
      </Row>

      {/* Action Buttons */}
      <Card style={{ marginBottom: 24 }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Space>
              <Button
                type="primary"
                icon={<CalendarOutlined />}
                onClick={handleScheduleTest}
              >
                Schedule Placement Test
              </Button>
              <Button
                icon={<PlusOutlined />}
                onClick={handleCreateTest}
              >
                Create New Test
              </Button>
              <Button icon={<BarChartOutlined />}>
                View Analytics
              </Button>
            </Space>
          </Col>
          <Col>
            <Space>
              <Button>Export Results</Button>
              <Button>Test Bank</Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* Tests Management Tabs */}
      <Card>
        <Tabs defaultActiveKey="1">
          <TabPane tab="Test Bank" key="1">
            <Table
              columns={testsColumns}
              dataSource={testsData || []}
              loading={testsLoading}
              rowKey="id"
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `Total ${total} tests`
              }}
            />
          </TabPane>

          <TabPane tab="Scheduled Tests" key="2">
            <Table
              columns={[
                {
                  title: 'Student',
                  key: 'student',
                  render: (_, record) => (
                    <div>
                      <Text strong>{record.student_name}</Text>
                      <br />
                      <Text type="secondary">{record.student_phone}</Text>
                    </div>
                  )
                },
                {
                  title: 'Test',
                  dataIndex: 'test_name',
                  key: 'test_name'
                },
                {
                  title: 'Date & Time',
                  key: 'datetime',
                  render: (_, record) => (
                    <div>
                      <Text>{dayjs(record.test_date).format('MMM DD, YYYY')}</Text>
                      <br />
                      <Text type="secondary">{record.test_time}</Text>
                    </div>
                  )
                },
                {
                  title: 'Status',
                  dataIndex: 'status',
                  key: 'status',
                  render: (status) => (
                    <Tag color={
                      status === 'scheduled' ? 'blue' :
                      status === 'in_progress' ? 'orange' :
                      status === 'completed' ? 'green' : 'red'
                    }>
                      {status.replace('_', ' ').toUpperCase()}
                    </Tag>
                  )
                },
                {
                  title: 'Actions',
                  key: 'actions',
                  render: (_, record) => (
                    <Space>
                      <Button size="small" icon={<EyeOutlined />}>
                        View
                      </Button>
                      <Button size="small" icon={<EditOutlined />}>
                        Edit
                      </Button>
                    </Space>
                  )
                }
              ]}
              dataSource={resultsData?.filter(r => r.status === 'scheduled') || []}
              loading={resultsLoading}
              rowKey="id"
              pagination={{ pageSize: 10 }}
            />
          </TabPane>

          <TabPane tab="Test Results" key="3">
            <Table
              columns={[
                {
                  title: 'Student',
                  key: 'student',
                  render: (_, record) => (
                    <div>
                      <Text strong>{record.student_name}</Text>
                      <br />
                      <Text type="secondary">{record.student_phone}</Text>
                    </div>
                  )
                },
                {
                  title: 'Test',
                  dataIndex: 'test_name',
                  key: 'test_name'
                },
                {
                  title: 'Score',
                  key: 'score',
                  render: (_, record) => (
                    <div>
                      <Text strong style={{
                        color: record.score >= record.passing_score ? '#52c41a' : '#ff4d4f'
                      }}>
                        {record.score} / {record.total_score}
                      </Text>
                      <br />
                      <Text type="secondary">
                        {((record.score / record.total_score) * 100).toFixed(1)}%
                      </Text>
                    </div>
                  )
                },
                {
                  title: 'Level Assigned',
                  dataIndex: 'assigned_level',
                  key: 'assigned_level',
                  render: (level) => level ? <Tag color="purple">{level}</Tag> : '-'
                },
                {
                  title: 'Date',
                  dataIndex: 'test_date',
                  key: 'test_date',
                  render: (date) => dayjs(date).format('MMM DD, YYYY')
                },
                {
                  title: 'Actions',
                  key: 'actions',
                  render: (_, record) => (
                    <Space>
                      <Button size="small" icon={<EyeOutlined />}>
                        View Details
                      </Button>
                      <Button size="small" icon={<FileTextOutlined />}>
                        Certificate
                      </Button>
                    </Space>
                  )
                }
              ]}
              dataSource={resultsData?.filter(r => r.status === 'completed') || []}
              loading={resultsLoading}
              rowKey="id"
              pagination={{ pageSize: 10 }}
            />
          </TabPane>
        </Tabs>
      </Card>

      {/* Create Test Modal */}
      <Modal
        title="Create New Test"
        open={isTestModalVisible}
        onCancel={() => setIsTestModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={testForm}
          layout="vertical"
          onFinish={handleTestSubmit}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="Test Name"
                rules={[{ required: true, message: 'Please enter test name' }]}
              >
                <Input placeholder="e.g., General English Placement Test" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="type"
                label="Test Type"
                rules={[{ required: true, message: 'Please select test type' }]}
              >
                <Select placeholder="Select test type">
                  <Option value="placement">Placement Test</Option>
                  <Option value="level_progression">Level Progression</Option>
                  <Option value="assessment">Assessment</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="level"
                label="Level"
                rules={[{ required: true, message: 'Please select level' }]}
              >
                <Select placeholder="Select level">
                  <Option value="A1">A1 - Beginner</Option>
                  <Option value="A2">A2 - Elementary</Option>
                  <Option value="B1">B1 - Intermediate</Option>
                  <Option value="B2">B2 - Upper Intermediate</Option>
                  <Option value="C1">C1 - Advanced</Option>
                  <Option value="C2">C2 - Proficiency</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="duration_minutes"
                label="Duration (minutes)"
                rules={[{ required: true, message: 'Please enter duration' }]}
              >
                <Input type="number" placeholder="e.g., 90" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="total_score"
                label="Total Score"
                rules={[{ required: true, message: 'Please enter total score' }]}
              >
                <Input type="number" placeholder="e.g., 100" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="passing_score"
                label="Passing Score"
                rules={[{ required: true, message: 'Please enter passing score' }]}
              >
                <Input type="number" placeholder="e.g., 70" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="description"
            label="Description"
          >
            <Input.TextArea
              rows={3}
              placeholder="Test description and instructions..."
            />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setIsTestModalVisible(false)}>
                Cancel
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={createTestMutation.isLoading}
              >
                Create Test
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* Schedule Test Modal */}
      <Modal
        title="Schedule Placement Test"
        open={isScheduleModalVisible}
        onCancel={() => setIsScheduleModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={scheduleForm}
          layout="vertical"
          onFinish={handleScheduleSubmit}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="student_id"
                label="Student"
                rules={[{ required: true, message: 'Please select student' }]}
              >
                <Select
                  placeholder="Select student"
                  showSearch
                  filterOption={(input, option) =>
                    option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                  }
                >
                  {studentsData?.students?.map(student => (
                    <Option key={student.id} value={student.id}>
                      {student.first_name} {student.last_name} - {student.phone}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="test_id"
                label="Test"
                rules={[{ required: true, message: 'Please select test' }]}
              >
                <Select placeholder="Select test">
                  {testsData?.filter(test => test.type === 'placement').map(test => (
                    <Option key={test.id} value={test.id}>
                      {test.name} ({test.level})
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="test_date"
                label="Test Date"
                rules={[{ required: true, message: 'Please select date' }]}
              >
                <DatePicker
                  style={{ width: '100%' }}
                  disabledDate={(current) => current && current < dayjs().startOf('day')}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="test_time"
                label="Test Time"
                rules={[{ required: true, message: 'Please select time' }]}
              >
                <TimePicker
                  style={{ width: '100%' }}
                  format="HH:mm"
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="notes"
            label="Notes"
          >
            <Input.TextArea
              rows={3}
              placeholder="Additional notes or instructions..."
            />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setIsScheduleModalVisible(false)}>
                Cancel
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={scheduleTestMutation.isLoading}
              >
                Schedule Test
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* Test Results Drawer */}
      <Drawer
        title="Test Details"
        placement="right"
        onClose={() => setIsResultsDrawerVisible(false)}
        open={isResultsDrawerVisible}
        width={600}
      >
        {selectedTest && (
          <div>
            <Descriptions title="Test Information" bordered column={1}>
              <Descriptions.Item label="Test Name">{selectedTest.name}</Descriptions.Item>
              <Descriptions.Item label="Type">
                <Tag color={
                  selectedTest.type === 'placement' ? 'blue' :
                  selectedTest.type === 'level_progression' ? 'green' : 'orange'
                }>
                  {selectedTest.type?.replace('_', ' ').toUpperCase()}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="Level">
                <Tag color="purple">{selectedTest.level}</Tag>
              </Descriptions.Item>
              <Descriptions.Item label="Duration">{selectedTest.duration_minutes} minutes</Descriptions.Item>
              <Descriptions.Item label="Total Score">{selectedTest.total_score}</Descriptions.Item>
              <Descriptions.Item label="Passing Score">{selectedTest.passing_score}</Descriptions.Item>
              <Descriptions.Item label="Status">
                <Badge
                  status={selectedTest.is_active ? 'success' : 'default'}
                  text={selectedTest.is_active ? 'Active' : 'Inactive'}
                />
              </Descriptions.Item>
            </Descriptions>

            {selectedTest.description && (
              <div style={{ marginTop: 16 }}>
                <Title level={5}>Description</Title>
                <Text>{selectedTest.description}</Text>
              </div>
            )}

            <div style={{ marginTop: 24, textAlign: 'center' }}>
              <Space>
                <Button type="primary" icon={<CalendarOutlined />}>
                  Schedule This Test
                </Button>
                <Button icon={<EditOutlined />}>
                  Edit Test
                </Button>
                <Button icon={<BarChartOutlined />}>
                  View Analytics
                </Button>
              </Space>
            </div>
          </div>
        )}
      </Drawer>
    </div>
  )
}

export default TestsPage
