{"name": "innovative-centre-crm-frontend", "version": "1.0.0", "description": "Frontend for Innovative Centre CRM System", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint src --ext .js,.jsx,.ts,.tsx", "format": "prettier --write src"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.1", "axios": "^1.6.2", "antd": "^5.12.8", "@ant-design/icons": "^5.2.6", "dayjs": "^1.11.10", "recharts": "^2.8.0", "react-query": "^3.39.3", "zustand": "^4.4.7", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "yup": "^1.4.0", "react-hot-toast": "^2.4.1", "socket.io-client": "^4.7.4"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react": "^4.2.1", "vite": "^5.0.8", "eslint": "^8.55.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "prettier": "^3.1.1", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32"}, "engines": {"node": ">=18.0.0"}}