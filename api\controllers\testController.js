const { query } = require('../config/database');
const Joi = require('joi');

// Validation schemas
const createTestSchema = Joi.object({
    name: Joi.string().required().min(3).max(255),
    type: Joi.string().valid('placement', 'level_progression', 'assessment').required(),
    level: Joi.string().valid('A1', 'A2', 'B1', 'B2', 'C1', 'C2').required(),
    duration_minutes: Joi.number().integer().min(15).max(300).required(),
    total_score: Joi.number().integer().min(1).max(1000).required(),
    passing_score: Joi.number().integer().min(1).required(),
    description: Joi.string().allow('').max(1000),
    course_id: Joi.number().integer().optional()
});

const scheduleTestSchema = Joi.object({
    student_id: Joi.number().integer().required(),
    test_id: Joi.number().integer().required(),
    test_date: Joi.date().required(),
    test_time: Joi.string().pattern(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/).required(),
    notes: Joi.string().allow('').max(500)
});

const submitResultSchema = Joi.object({
    test_result_id: Joi.number().integer().required(),
    score: Joi.number().integer().min(0).required(),
    skill_scores: Joi.object({
        reading: Joi.number().integer().min(0).optional(),
        writing: Joi.number().integer().min(0).optional(),
        listening: Joi.number().integer().min(0).optional(),
        speaking: Joi.number().integer().min(0).optional()
    }).optional(),
    assigned_level: Joi.string().valid('A1', 'A2', 'B1', 'B2', 'C1', 'C2').optional(),
    feedback: Joi.string().allow('').max(1000)
});

// Get all tests with filtering
const getTests = async (filters = {}) => {
    try {
        const { type, level, is_active } = filters;
        
        let whereConditions = [];
        let queryParams = [];
        let paramIndex = 1;

        if (type) {
            whereConditions.push(`type = $${paramIndex++}`);
            queryParams.push(type);
        }

        if (level) {
            whereConditions.push(`level = $${paramIndex++}`);
            queryParams.push(level);
        }

        if (is_active !== undefined) {
            whereConditions.push(`is_active = $${paramIndex++}`);
            queryParams.push(is_active === 'true');
        }

        const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

        const testsQuery = `
            SELECT 
                t.id,
                t.name,
                t.type,
                t.level,
                t.duration_minutes,
                t.total_score,
                t.passing_score,
                t.description,
                t.is_active,
                t.created_at,
                c.name as course_name
            FROM tests t
            LEFT JOIN courses c ON t.course_id = c.id
            ${whereClause}
            ORDER BY t.type, t.level, t.name
        `;

        const result = await query(testsQuery, queryParams);

        return {
            success: true,
            data: result.rows
        };
    } catch (error) {
        console.error('Get tests error:', error);
        return {
            success: false,
            message: 'Failed to retrieve tests'
        };
    }
};

// Get test by ID
const getTestById = async (testId) => {
    try {
        const testQuery = `
            SELECT 
                t.*,
                c.name as course_name
            FROM tests t
            LEFT JOIN courses c ON t.course_id = c.id
            WHERE t.id = $1
        `;

        const result = await query(testQuery, [testId]);

        if (result.rows.length === 0) {
            return {
                success: false,
                message: 'Test not found'
            };
        }

        return {
            success: true,
            data: result.rows[0]
        };
    } catch (error) {
        console.error('Get test by ID error:', error);
        return {
            success: false,
            message: 'Failed to retrieve test'
        };
    }
};

// Create new test
const createTest = async (testData) => {
    try {
        // Validate input data
        const { error, value } = createTestSchema.validate(testData);
        if (error) {
            return {
                success: false,
                message: error.details[0].message
            };
        }

        const {
            name, type, level, duration_minutes, total_score, 
            passing_score, description, course_id
        } = value;

        // Validate passing score is not greater than total score
        if (passing_score > total_score) {
            return {
                success: false,
                message: 'Passing score cannot be greater than total score'
            };
        }

        // Check if course exists (if provided)
        if (course_id) {
            const courseCheck = await query(
                'SELECT id FROM courses WHERE id = $1 AND is_active = true',
                [course_id]
            );

            if (courseCheck.rows.length === 0) {
                return {
                    success: false,
                    message: 'Course not found or inactive'
                };
            }
        }

        const insertQuery = `
            INSERT INTO tests (
                name, type, level, duration_minutes, total_score, 
                passing_score, description, course_id, is_active
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, true)
            RETURNING *
        `;

        const result = await query(insertQuery, [
            name, type, level, duration_minutes, total_score,
            passing_score, description || null, course_id || null
        ]);

        return {
            success: true,
            data: result.rows[0],
            message: 'Test created successfully'
        };
    } catch (error) {
        console.error('Create test error:', error);
        return {
            success: false,
            message: 'Failed to create test'
        };
    }
};

// Schedule test for student
const scheduleTest = async (scheduleData) => {
    try {
        // Validate input data
        const { error, value } = scheduleTestSchema.validate(scheduleData);
        if (error) {
            return {
                success: false,
                message: error.details[0].message
            };
        }

        const { student_id, test_id, test_date, test_time, notes } = value;

        // Check if student exists
        const studentCheck = await query(
            'SELECT id FROM students WHERE id = $1',
            [student_id]
        );

        if (studentCheck.rows.length === 0) {
            return {
                success: false,
                message: 'Student not found'
            };
        }

        // Check if test exists
        const testCheck = await query(
            'SELECT id, name FROM tests WHERE id = $1 AND is_active = true',
            [test_id]
        );

        if (testCheck.rows.length === 0) {
            return {
                success: false,
                message: 'Test not found or inactive'
            };
        }

        // Check if student already has a scheduled test for the same date
        const existingTest = await query(
            'SELECT id FROM test_results WHERE student_id = $1 AND test_date = $2 AND status = $3',
            [student_id, test_date, 'scheduled']
        );

        if (existingTest.rows.length > 0) {
            return {
                success: false,
                message: 'Student already has a test scheduled for this date'
            };
        }

        const insertQuery = `
            INSERT INTO test_results (
                student_id, test_id, test_date, test_time, 
                status, notes, created_at
            ) VALUES ($1, $2, $3, $4, 'scheduled', $5, NOW())
            RETURNING *
        `;

        const result = await query(insertQuery, [
            student_id, test_id, test_date, test_time, notes || null
        ]);

        return {
            success: true,
            data: result.rows[0],
            message: 'Test scheduled successfully'
        };
    } catch (error) {
        console.error('Schedule test error:', error);
        return {
            success: false,
            message: 'Failed to schedule test'
        };
    }
};

// Get test results with filtering
const getTestResults = async (filters = {}) => {
    try {
        const { status, student_id, test_id, date_from, date_to } = filters;
        
        let whereConditions = [];
        let queryParams = [];
        let paramIndex = 1;

        if (status) {
            whereConditions.push(`tr.status = $${paramIndex++}`);
            queryParams.push(status);
        }

        if (student_id) {
            whereConditions.push(`tr.student_id = $${paramIndex++}`);
            queryParams.push(student_id);
        }

        if (test_id) {
            whereConditions.push(`tr.test_id = $${paramIndex++}`);
            queryParams.push(test_id);
        }

        if (date_from) {
            whereConditions.push(`tr.test_date >= $${paramIndex++}`);
            queryParams.push(date_from);
        }

        if (date_to) {
            whereConditions.push(`tr.test_date <= $${paramIndex++}`);
            queryParams.push(date_to);
        }

        const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

        const resultsQuery = `
            SELECT 
                tr.id,
                tr.student_id,
                tr.test_id,
                tr.test_date,
                tr.test_time,
                tr.status,
                tr.score,
                tr.skill_scores,
                tr.assigned_level,
                tr.feedback,
                tr.notes,
                tr.created_at,
                tr.completed_at,
                u.first_name as student_first_name,
                u.last_name as student_last_name,
                u.phone as student_phone,
                s.student_id as student_number,
                t.name as test_name,
                t.type as test_type,
                t.level as test_level,
                t.total_score,
                t.passing_score
            FROM test_results tr
            JOIN students s ON tr.student_id = s.id
            JOIN users u ON s.user_id = u.id
            JOIN tests t ON tr.test_id = t.id
            ${whereClause}
            ORDER BY tr.test_date DESC, tr.created_at DESC
        `;

        const result = await query(resultsQuery, queryParams);

        return {
            success: true,
            data: result.rows
        };
    } catch (error) {
        console.error('Get test results error:', error);
        return {
            success: false,
            message: 'Failed to retrieve test results'
        };
    }
};

module.exports = {
    getTests,
    getTestById,
    createTest,
    scheduleTest,
    getTestResults
};
