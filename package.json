{"name": "innovative-centre-crm", "version": "1.0.0", "description": "Comprehensive CRM system for Innovative Centre English language center", "main": "api/index.js", "scripts": {"dev": "vercel dev", "build": "npm run build:frontend && npm run build:api", "build:frontend": "cd frontend && npm run build", "build:api": "echo 'API build complete'", "start": "vercel start", "test": "jest", "lint": "eslint .", "format": "prettier --write ."}, "keywords": ["crm", "education", "english-learning", "student-management", "uzbekistan"], "author": "Innovative Centre", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "dotenv": "^16.3.1", "pg": "^8.11.3", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "joi": "^17.11.0", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.7", "twilio": "^4.19.0", "moment": "^2.29.4", "uuid": "^9.0.1", "compression": "^1.7.4", "rate-limiter-flexible": "^3.0.8"}, "devDependencies": {"jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.54.0", "prettier": "^3.1.0", "nodemon": "^3.0.2"}, "engines": {"node": ">=18.0.0"}, "repository": {"type": "git", "url": "https://github.com/MrFarrukhT/Main-Landing-Page.git"}}