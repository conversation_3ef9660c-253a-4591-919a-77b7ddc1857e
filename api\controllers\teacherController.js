const { query } = require('../config/database');
const Joi = require('joi');
const bcrypt = require('bcryptjs');

// Validation schemas
const createTeacherSchema = Joi.object({
    first_name: Joi.string().required().min(2).max(50),
    last_name: Joi.string().required().min(2).max(50),
    phone: Joi.string().required().pattern(/^\+998\d{9}$/),
    email: Joi.string().email().optional().allow(''),
    employee_id: Joi.string().required().min(2).max(20),
    experience_years: Joi.number().integer().min(0).max(50).required(),
    specializations: Joi.string().required(),
    qualification: Joi.string().allow('').max(255),
    bio: Joi.string().allow('').max(1000),
    password: Joi.string().min(6).optional()
});

const updateTeacherSchema = Joi.object({
    first_name: Joi.string().min(2).max(50).optional(),
    last_name: Joi.string().min(2).max(50).optional(),
    phone: Joi.string().pattern(/^\+998\d{9}$/).optional(),
    email: Joi.string().email().optional().allow(''),
    employee_id: Joi.string().min(2).max(20).optional(),
    experience_years: Joi.number().integer().min(0).max(50).optional(),
    specializations: Joi.string().optional(),
    qualification: Joi.string().allow('').max(255).optional(),
    bio: Joi.string().allow('').max(1000).optional(),
    is_active: Joi.boolean().optional()
});

// Get all teachers with filtering
const getTeachers = async (filters = {}) => {
    try {
        const { specialization, is_active, search } = filters;
        
        let whereConditions = [];
        let queryParams = [];
        let paramIndex = 1;

        if (specialization) {
            whereConditions.push(`t.specializations ILIKE $${paramIndex++}`);
            queryParams.push(`%${specialization}%`);
        }

        if (is_active !== undefined) {
            whereConditions.push(`t.is_active = $${paramIndex++}`);
            queryParams.push(is_active === 'true');
        }

        if (search) {
            whereConditions.push(`(u.first_name ILIKE $${paramIndex} OR u.last_name ILIKE $${paramIndex} OR t.employee_id ILIKE $${paramIndex})`);
            queryParams.push(`%${search}%`);
            paramIndex++;
        }

        const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

        const teachersQuery = `
            SELECT 
                t.id,
                t.employee_id,
                u.first_name,
                u.last_name,
                u.phone,
                u.email,
                t.specializations,
                t.experience_years,
                t.qualification,
                t.bio,
                t.photo_url,
                t.rating,
                t.is_active,
                t.created_at,
                COUNT(g.id) as active_groups
            FROM teachers t
            JOIN users u ON t.user_id = u.id
            LEFT JOIN groups g ON t.id = g.teacher_id AND g.is_active = true
            ${whereClause}
            GROUP BY t.id, u.first_name, u.last_name, u.phone, u.email, 
                     t.employee_id, t.specializations, t.experience_years, 
                     t.qualification, t.bio, t.photo_url, t.rating, t.is_active, t.created_at
            ORDER BY u.first_name, u.last_name
        `;

        const result = await query(teachersQuery, queryParams);

        return {
            success: true,
            data: result.rows
        };
    } catch (error) {
        console.error('Get teachers error:', error);
        return {
            success: false,
            message: 'Failed to retrieve teachers'
        };
    }
};

// Get teacher by ID
const getTeacherById = async (teacherId) => {
    try {
        const teacherQuery = `
            SELECT 
                t.*,
                u.first_name,
                u.last_name,
                u.phone,
                u.email,
                COUNT(g.id) as active_groups
            FROM teachers t
            JOIN users u ON t.user_id = u.id
            LEFT JOIN groups g ON t.id = g.teacher_id AND g.is_active = true
            WHERE t.id = $1
            GROUP BY t.id, u.first_name, u.last_name, u.phone, u.email
        `;

        const result = await query(teacherQuery, [teacherId]);

        if (result.rows.length === 0) {
            return {
                success: false,
                message: 'Teacher not found'
            };
        }

        return {
            success: true,
            data: result.rows[0]
        };
    } catch (error) {
        console.error('Get teacher by ID error:', error);
        return {
            success: false,
            message: 'Failed to retrieve teacher'
        };
    }
};

// Create new teacher
const createTeacher = async (teacherData) => {
    try {
        // Validate input data
        const { error, value } = createTeacherSchema.validate(teacherData);
        if (error) {
            return {
                success: false,
                message: error.details[0].message
            };
        }

        const {
            first_name, last_name, phone, email, employee_id,
            experience_years, specializations, qualification, bio, password
        } = value;

        // Check if user with same phone already exists
        const existingUser = await query(
            'SELECT id FROM users WHERE phone = $1',
            [phone]
        );

        if (existingUser.rows.length > 0) {
            return {
                success: false,
                message: 'User with this phone number already exists'
            };
        }

        // Check if employee ID already exists
        const existingTeacher = await query(
            'SELECT id FROM teachers WHERE employee_id = $1',
            [employee_id]
        );

        if (existingTeacher.rows.length > 0) {
            return {
                success: false,
                message: 'Teacher with this employee ID already exists'
            };
        }

        // Hash password if provided, otherwise generate a default one
        const defaultPassword = password || 'teacher123';
        const hashedPassword = await bcrypt.hash(defaultPassword, 10);

        // Start transaction
        await query('BEGIN');

        try {
            // Create user first
            const userResult = await query(`
                INSERT INTO users (
                    first_name, last_name, phone, email, password, role
                ) VALUES ($1, $2, $3, $4, $5, 'teacher')
                RETURNING id
            `, [first_name, last_name, phone, email || null, hashedPassword]);

            const userId = userResult.rows[0].id;

            // Create teacher record
            const teacherResult = await query(`
                INSERT INTO teachers (
                    user_id, employee_id, experience_years, specializations,
                    qualification, bio, is_active
                ) VALUES ($1, $2, $3, $4, $5, $6, true)
                RETURNING *
            `, [userId, employee_id, experience_years, specializations, qualification || null, bio || null]);

            await query('COMMIT');

            return {
                success: true,
                data: {
                    ...teacherResult.rows[0],
                    first_name,
                    last_name,
                    phone,
                    email
                },
                message: 'Teacher created successfully'
            };
        } catch (error) {
            await query('ROLLBACK');
            throw error;
        }
    } catch (error) {
        console.error('Create teacher error:', error);
        return {
            success: false,
            message: 'Failed to create teacher'
        };
    }
};

// Update teacher
const updateTeacher = async (teacherId, teacherData) => {
    try {
        // Validate input data
        const { error, value } = updateTeacherSchema.validate(teacherData);
        if (error) {
            return {
                success: false,
                message: error.details[0].message
            };
        }

        // Check if teacher exists
        const teacherCheck = await query(
            'SELECT user_id FROM teachers WHERE id = $1',
            [teacherId]
        );

        if (teacherCheck.rows.length === 0) {
            return {
                success: false,
                message: 'Teacher not found'
            };
        }

        const userId = teacherCheck.rows[0].user_id;

        // Start transaction
        await query('BEGIN');

        try {
            // Update user information if provided
            const userFields = ['first_name', 'last_name', 'phone', 'email'];
            const userUpdates = [];
            const userParams = [];
            let userParamIndex = 1;

            userFields.forEach(field => {
                if (value[field] !== undefined) {
                    userUpdates.push(`${field} = $${userParamIndex++}`);
                    userParams.push(value[field] || null);
                }
            });

            if (userUpdates.length > 0) {
                userParams.push(userId);
                await query(`
                    UPDATE users 
                    SET ${userUpdates.join(', ')}, updated_at = NOW()
                    WHERE id = $${userParamIndex}
                `, userParams);
            }

            // Update teacher information
            const teacherFields = ['employee_id', 'experience_years', 'specializations', 'qualification', 'bio', 'is_active'];
            const teacherUpdates = [];
            const teacherParams = [];
            let teacherParamIndex = 1;

            teacherFields.forEach(field => {
                if (value[field] !== undefined) {
                    teacherUpdates.push(`${field} = $${teacherParamIndex++}`);
                    teacherParams.push(value[field]);
                }
            });

            if (teacherUpdates.length > 0) {
                teacherParams.push(teacherId);
                await query(`
                    UPDATE teachers 
                    SET ${teacherUpdates.join(', ')}, updated_at = NOW()
                    WHERE id = $${teacherParamIndex}
                `, teacherParams);
            }

            await query('COMMIT');

            // Get updated teacher data
            const updatedTeacher = await getTeacherById(teacherId);

            return {
                success: true,
                data: updatedTeacher.data,
                message: 'Teacher updated successfully'
            };
        } catch (error) {
            await query('ROLLBACK');
            throw error;
        }
    } catch (error) {
        console.error('Update teacher error:', error);
        return {
            success: false,
            message: 'Failed to update teacher'
        };
    }
};

module.exports = {
    getTeachers,
    getTeacherById,
    createTeacher,
    updateTeacher
};
