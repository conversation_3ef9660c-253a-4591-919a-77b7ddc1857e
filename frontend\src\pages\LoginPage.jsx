import React, { useState, useEffect } from 'react'
import { Form, Input, But<PERSON>, Card, Typography, Alert, Space } from 'antd'
import { UserOutlined, LockOutlined, PhoneOutlined } from '@ant-design/icons'
import { useAuthStore } from '../stores/authStore'
import { useNavigate } from 'react-router-dom'

const { Title, Text } = Typography

const LoginPage = () => {
  const [form] = Form.useForm()
  const { login, isLoading, isAuthenticated } = useAuthStore()
  const [error, setError] = useState('')
  const navigate = useNavigate()

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      navigate('/dashboard')
    }
  }, [isAuthenticated, navigate])

  const handleSubmit = async (values) => {
    setError('')
    
    const result = await login({
      phone: values.phone,
      password: values.password
    })

    if (!result.success) {
      setError(result.message || 'Login failed. Please try again.')
    }
  }

  const handlePhoneChange = (e) => {
    let value = e.target.value.replace(/\D/g, '') // Remove non-digits
    
    // Format Uzbek phone number: +998 XX XXX XX XX
    if (value.startsWith('998')) {
      value = value.substring(3)
    }
    
    if (value.length <= 9) {
      // Format as XX XXX XX XX
      if (value.length > 2) {
        value = value.substring(0, 2) + ' ' + value.substring(2)
      }
      if (value.length > 6) {
        value = value.substring(0, 6) + ' ' + value.substring(6)
      }
      if (value.length > 9) {
        value = value.substring(0, 9) + ' ' + value.substring(9)
      }
      
      form.setFieldValue('phone', value)
    }
  }

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #0056b3 0%, #004494 100%)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      padding: '20px'
    }}>
      <Card
        style={{
          width: '100%',
          maxWidth: 400,
          borderRadius: 16,
          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
          border: 'none'
        }}
        bodyStyle={{ padding: '40px 32px' }}
      >
        <div style={{ textAlign: 'center', marginBottom: 32 }}>
          <div style={{
            width: 80,
            height: 80,
            background: 'linear-gradient(135deg, #0056b3, #3378c5)',
            borderRadius: '50%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            margin: '0 auto 16px',
            boxShadow: '0 4px 16px rgba(0, 86, 179, 0.3)'
          }}>
            <UserOutlined style={{ fontSize: 32, color: 'white' }} />
          </div>
          <Title level={2} style={{ margin: 0, color: '#333' }}>
            Innovative Centre
          </Title>
          <Text type="secondary" style={{ fontSize: 16 }}>
            CRM System Login
          </Text>
        </div>

        {error && (
          <Alert
            message={error}
            type="error"
            showIcon
            style={{ marginBottom: 24, borderRadius: 8 }}
          />
        )}

        <Form
          form={form}
          name="login"
          onFinish={handleSubmit}
          layout="vertical"
          size="large"
          autoComplete="off"
        >
          <Form.Item
            name="phone"
            label="Phone Number"
            rules={[
              { required: true, message: 'Please enter your phone number' },
              { 
                pattern: /^\d{2}\s\d{3}\s\d{2}\s\d{2}$/, 
                message: 'Please enter a valid phone number (XX XXX XX XX)' 
              }
            ]}
          >
            <Input
              prefix={
                <Space>
                  <PhoneOutlined style={{ color: '#0056b3' }} />
                  <Text strong style={{ color: '#666' }}>+998</Text>
                </Space>
              }
              placeholder="XX XXX XX XX"
              onChange={handlePhoneChange}
              style={{ borderRadius: 8 }}
            />
          </Form.Item>

          <Form.Item
            name="password"
            label="Password"
            rules={[
              { required: true, message: 'Please enter your password' },
              { min: 6, message: 'Password must be at least 6 characters' }
            ]}
          >
            <Input.Password
              prefix={<LockOutlined style={{ color: '#0056b3' }} />}
              placeholder="Enter your password"
              style={{ borderRadius: 8 }}
            />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0 }}>
            <Button
              type="primary"
              htmlType="submit"
              loading={isLoading}
              block
              style={{
                height: 48,
                borderRadius: 8,
                fontSize: 16,
                fontWeight: 600,
                background: 'linear-gradient(135deg, #0056b3, #3378c5)',
                border: 'none',
                boxShadow: '0 4px 16px rgba(0, 86, 179, 0.3)'
              }}
            >
              {isLoading ? 'Signing In...' : 'Sign In'}
            </Button>
          </Form.Item>
        </Form>

        <div style={{ 
          textAlign: 'center', 
          marginTop: 24, 
          padding: '16px 0',
          borderTop: '1px solid #f0f0f0'
        }}>
          <Text type="secondary" style={{ fontSize: 12 }}>
            Innovative Centre CRM System v1.0
          </Text>
          <br />
          <Text type="secondary" style={{ fontSize: 12 }}>
            For support, contact: +998 55 701 01 06
          </Text>
        </div>
      </Card>
    </div>
  )
}

export default LoginPage
