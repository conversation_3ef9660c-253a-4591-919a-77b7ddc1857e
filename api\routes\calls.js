const express = require('express');
const router = express.Router();
const { query } = require('../config/database');
const { authenticate, requirePermission } = require('../middleware/auth');

// All routes require authentication
router.use(authenticate);

// Get all call logs
router.get('/', requirePermission('calls:read'), async (req, res) => {
    try {
        const { agent_id, lead_id, date_from, date_to } = req.query;
        
        let whereConditions = [];
        let queryParams = [];
        let paramIndex = 1;

        if (agent_id) {
            whereConditions.push(`cl.agent_id = $${paramIndex++}`);
            queryParams.push(agent_id);
        }

        if (lead_id) {
            whereConditions.push(`cl.lead_id = $${paramIndex++}`);
            queryParams.push(lead_id);
        }

        if (date_from) {
            whereConditions.push(`cl.created_at >= $${paramIndex++}`);
            queryParams.push(date_from);
        }

        if (date_to) {
            whereConditions.push(`cl.created_at <= $${paramIndex++}`);
            queryParams.push(date_to);
        }

        const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

        const callsQuery = `
            SELECT 
                cl.id,
                cl.phone_number,
                cl.call_type,
                cl.duration_seconds,
                cl.outcome,
                cl.notes,
                cl.follow_up_required,
                cl.follow_up_date,
                cl.created_at,
                l.name as lead_name,
                u.first_name as agent_first_name,
                u.last_name as agent_last_name
            FROM call_logs cl
            LEFT JOIN leads l ON cl.lead_id = l.id
            LEFT JOIN users u ON cl.agent_id = u.id
            ${whereClause}
            ORDER BY cl.created_at DESC
        `;

        const result = await query(callsQuery, queryParams);

        res.json({
            success: true,
            data: result.rows
        });
    } catch (error) {
        console.error('Get call logs error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to retrieve call logs'
        });
    }
});

// Get call log by ID
router.get('/:id', requirePermission('calls:read'), async (req, res) => {
    try {
        const callQuery = `
            SELECT 
                cl.*,
                l.name as lead_name,
                l.phone as lead_phone,
                u.first_name as agent_first_name,
                u.last_name as agent_last_name
            FROM call_logs cl
            LEFT JOIN leads l ON cl.lead_id = l.id
            LEFT JOIN users u ON cl.agent_id = u.id
            WHERE cl.id = $1
        `;

        const result = await query(callQuery, [req.params.id]);

        if (result.rows.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'Call log not found'
            });
        }

        res.json({
            success: true,
            data: result.rows[0]
        });
    } catch (error) {
        console.error('Get call log error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to retrieve call log'
        });
    }
});

// Placeholder for other call endpoints
router.post('/', requirePermission('calls:create'), (req, res) => {
    res.status(501).json({
        success: false,
        message: 'Call log creation endpoint not yet implemented'
    });
});

router.put('/:id', requirePermission('calls:update'), (req, res) => {
    res.status(501).json({
        success: false,
        message: 'Call log update endpoint not yet implemented'
    });
});

module.exports = router;
