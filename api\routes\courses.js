const express = require('express');
const router = express.Router();
const { query } = require('../config/database');
const { authenticate, requirePermission } = require('../middleware/auth');

// All routes require authentication
router.use(authenticate);

// Get all courses
router.get('/', requirePermission('courses:read'), async (req, res) => {
    try {
        const coursesQuery = `
            SELECT 
                id,
                name,
                code,
                type,
                level,
                description,
                duration_weeks,
                classes_per_week,
                class_duration_minutes,
                max_students,
                min_students,
                price,
                is_active
            FROM courses
            WHERE is_active = true
            ORDER BY type, level
        `;

        const result = await query(coursesQuery);

        res.json({
            success: true,
            data: result.rows
        });
    } catch (error) {
        console.error('Get courses error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to retrieve courses'
        });
    }
});

// Get course by ID
router.get('/:id', requirePermission('courses:read'), async (req, res) => {
    try {
        const courseQuery = `
            SELECT * FROM courses WHERE id = $1
        `;

        const result = await query(courseQuery, [req.params.id]);

        if (result.rows.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'Course not found'
            });
        }

        res.json({
            success: true,
            data: result.rows[0]
        });
    } catch (error) {
        console.error('Get course error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to retrieve course'
        });
    }
});

// Placeholder for other course endpoints
router.post('/', requirePermission('courses:create'), (req, res) => {
    res.status(501).json({
        success: false,
        message: 'Course creation endpoint not yet implemented'
    });
});

router.put('/:id', requirePermission('courses:update'), (req, res) => {
    res.status(501).json({
        success: false,
        message: 'Course update endpoint not yet implemented'
    });
});

module.exports = router;
