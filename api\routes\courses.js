const express = require('express');
const router = express.Router();
const courseController = require('../controllers/courseController');
const { authenticate, requirePermission } = require('../middleware/auth');

// All routes require authentication
router.use(authenticate);

// Get all courses
router.get('/', requirePermission('courses:read'), async (req, res) => {
    try {
        const result = await courseController.getCourses(req.query);

        if (result.success) {
            res.json(result);
        } else {
            res.status(400).json(result);
        }
    } catch (error) {
        console.error('Get courses error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to retrieve courses'
        });
    }
});

// Get course statistics
router.get('/statistics', requirePermission('courses:read'), async (req, res) => {
    try {
        const result = await courseController.getCourseStatistics();

        if (result.success) {
            res.json(result);
        } else {
            res.status(400).json(result);
        }
    } catch (error) {
        console.error('Get course statistics error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to retrieve course statistics'
        });
    }
});

// Get course by ID
router.get('/:id', requirePermission('courses:read'), async (req, res) => {
    try {
        const result = await courseController.getCourseById(req.params.id);

        if (result.success) {
            res.json(result);
        } else {
            res.status(404).json(result);
        }
    } catch (error) {
        console.error('Get course error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to retrieve course'
        });
    }
});

// Create new course
router.post('/', requirePermission('courses:create'), async (req, res) => {
    try {
        const result = await courseController.createCourse(req.body);

        if (result.success) {
            res.status(201).json(result);
        } else {
            res.status(400).json(result);
        }
    } catch (error) {
        console.error('Create course error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to create course'
        });
    }
});

// Update course
router.put('/:id', requirePermission('courses:update'), async (req, res) => {
    try {
        const result = await courseController.updateCourse(req.params.id, req.body);

        if (result.success) {
            res.json(result);
        } else {
            res.status(400).json(result);
        }
    } catch (error) {
        console.error('Update course error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to update course'
        });
    }
});

module.exports = router;
