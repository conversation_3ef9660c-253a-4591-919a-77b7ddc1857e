const axios = require('axios');
require('dotenv').config();

// SMS service configuration for Uzbek providers
const SMS_PROVIDERS = {
    ESKIZ: 'eskiz',
    MYTELECOM: 'mytelecom'
};

// Default provider
const DEFAULT_PROVIDER = SMS_PROVIDERS.ESKIZ;

// Eskiz.uz SMS service
class EskizSMSService {
    constructor() {
        this.baseURL = 'https://notify.eskiz.uz/api';
        this.token = null;
        this.email = process.env.ESKIZ_EMAIL;
        this.password = process.env.ESKIZ_PASSWORD;
    }

    async authenticate() {
        try {
            if (!this.email || !this.password) {
                throw new Error('Eskiz credentials not configured');
            }

            const response = await axios.post(`${this.baseURL}/auth/login`, {
                email: this.email,
                password: this.password
            });

            if (response.data && response.data.data && response.data.data.token) {
                this.token = response.data.data.token;
                return true;
            }

            throw new Error('Failed to get authentication token');
        } catch (error) {
            console.error('Eskiz authentication error:', error.message);
            return false;
        }
    }

    async sendSMS(phone, message) {
        try {
            // Ensure we have a valid token
            if (!this.token) {
                const authenticated = await this.authenticate();
                if (!authenticated) {
                    throw new Error('Failed to authenticate with Eskiz');
                }
            }

            // Format phone number for Uzbekistan
            const formattedPhone = this.formatUzbekPhone(phone);

            const response = await axios.post(
                `${this.baseURL}/message/sms/send`,
                {
                    mobile_phone: formattedPhone,
                    message: message,
                    from: '4546' // Default sender ID for Eskiz
                },
                {
                    headers: {
                        'Authorization': `Bearer ${this.token}`,
                        'Content-Type': 'application/json'
                    }
                }
            );

            if (response.data && response.data.status === 'success') {
                return {
                    success: true,
                    messageId: response.data.data.id,
                    provider: 'eskiz'
                };
            }

            throw new Error(response.data?.message || 'Failed to send SMS');
        } catch (error) {
            console.error('Eskiz SMS sending error:', error.message);
            
            // If token expired, try to re-authenticate and retry once
            if (error.response?.status === 401 && this.token) {
                this.token = null;
                const authenticated = await this.authenticate();
                if (authenticated) {
                    return this.sendSMS(phone, message); // Retry once
                }
            }

            return {
                success: false,
                error: error.message,
                provider: 'eskiz'
            };
        }
    }

    formatUzbekPhone(phone) {
        // Remove all non-digit characters
        let cleaned = phone.replace(/\D/g, '');
        
        // If starts with 998, it's already in correct format
        if (cleaned.startsWith('998')) {
            return cleaned;
        }
        
        // If starts with +998, remove the +
        if (phone.startsWith('+998')) {
            return cleaned;
        }
        
        // If it's a local number (9 digits), add country code
        if (cleaned.length === 9) {
            return '998' + cleaned;
        }
        
        return cleaned;
    }
}

// MyTelecom.uz SMS service (alternative provider)
class MyTelecomSMSService {
    constructor() {
        this.baseURL = 'https://api.mytelecom.uz';
        this.apiKey = process.env.MYTELECOM_API_KEY;
    }

    async sendSMS(phone, message) {
        try {
            if (!this.apiKey) {
                throw new Error('MyTelecom API key not configured');
            }

            const formattedPhone = this.formatUzbekPhone(phone);

            const response = await axios.post(
                `${this.baseURL}/sms/send`,
                {
                    phone: formattedPhone,
                    text: message
                },
                {
                    headers: {
                        'Authorization': `Bearer ${this.apiKey}`,
                        'Content-Type': 'application/json'
                    }
                }
            );

            if (response.data && response.data.success) {
                return {
                    success: true,
                    messageId: response.data.id,
                    provider: 'mytelecom'
                };
            }

            throw new Error(response.data?.message || 'Failed to send SMS');
        } catch (error) {
            console.error('MyTelecom SMS sending error:', error.message);
            return {
                success: false,
                error: error.message,
                provider: 'mytelecom'
            };
        }
    }

    formatUzbekPhone(phone) {
        // Same formatting logic as Eskiz
        let cleaned = phone.replace(/\D/g, '');
        
        if (cleaned.startsWith('998')) {
            return cleaned;
        }
        
        if (phone.startsWith('+998')) {
            return cleaned;
        }
        
        if (cleaned.length === 9) {
            return '998' + cleaned;
        }
        
        return cleaned;
    }
}

// Main SMS service class
class SMSService {
    constructor() {
        this.providers = {
            [SMS_PROVIDERS.ESKIZ]: new EskizSMSService(),
            [SMS_PROVIDERS.MYTELECOM]: new MyTelecomSMSService()
        };
        this.currentProvider = DEFAULT_PROVIDER;
    }

    async sendSMS(phone, message, provider = null) {
        const selectedProvider = provider || this.currentProvider;
        const smsProvider = this.providers[selectedProvider];

        if (!smsProvider) {
            throw new Error(`SMS provider ${selectedProvider} not found`);
        }

        return await smsProvider.sendSMS(phone, message);
    }

    // Send welcome SMS to new leads
    async sendWelcomeSMS(phone, name) {
        const message = `Salom ${name}! Innovative Centre'ga xush kelibsiz! Bizning mutaxassislarimiz tez orada siz bilan bog'lanishadi. Ma'lumot: +998 55 701 01 06`;
        
        return await this.sendSMS(phone, message);
    }

    // Send test reminder SMS
    async sendTestReminderSMS(phone, name, testDate, testTime) {
        const message = `Hurmatli ${name}! Sizning darajangizni aniqlash testi ${testDate} kuni soat ${testTime}da belgilangan. Manzil: Innovative Centre. Ma'lumot: +998 55 701 01 06`;
        
        return await this.sendSMS(phone, message);
    }

    // Send payment reminder SMS
    async sendPaymentReminderSMS(phone, name, amount, dueDate) {
        const message = `Hurmatli ${name}! Sizning ${amount} so'm miqdoridagi to'lovingiz ${dueDate} gacha amalga oshirilishi kerak. Ma'lumot: +998 55 701 01 06`;
        
        return await this.sendSMS(phone, message);
    }

    // Send class schedule SMS
    async sendClassScheduleSMS(phone, name, className, schedule) {
        const message = `Hurmatli ${name}! ${className} darslari jadvali: ${schedule}. Innovative Centre. Ma'lumot: +998 55 701 01 06`;
        
        return await this.sendSMS(phone, message);
    }

    // Send general notification SMS
    async sendNotificationSMS(phone, message) {
        const fullMessage = `${message} - Innovative Centre. Ma'lumot: +998 55 701 01 06`;
        
        return await this.sendSMS(phone, fullMessage);
    }

    // Switch SMS provider (for failover)
    switchProvider(provider) {
        if (this.providers[provider]) {
            this.currentProvider = provider;
            console.log(`SMS provider switched to: ${provider}`);
        } else {
            console.error(`Invalid SMS provider: ${provider}`);
        }
    }

    // Get current provider status
    getCurrentProvider() {
        return this.currentProvider;
    }

    // Test SMS functionality
    async testSMS(phone) {
        const testMessage = "Bu Innovative Centre CRM tizimidan test xabari. Agar siz bu xabarni olgan bo'lsangiz, tizim to'g'ri ishlayapti.";
        
        return await this.sendSMS(phone, testMessage);
    }
}

// Create singleton instance
const smsService = new SMSService();

module.exports = smsService;
