import React, { useState } from 'react'
import {
  Card,
  Table,
  Button,
  Input,
  Select,
  Space,
  Tag,
  Modal,
  Form,
  DatePicker,
  Typography,
  Row,
  Col,
  Statistic,
  Tooltip,
  message
} from 'antd'
import {
  SearchOutlined,
  PhoneOutlined,
  UserAddOutlined,
  EditOutlined,
  CalendarOutlined,
  MessageOutlined
} from '@ant-design/icons'
import { useQuery, useMutation, useQueryClient } from 'react-query'
import { useAuthStore } from '../stores/authStore'
import axios from 'axios'
import dayjs from 'dayjs'

const { Title, Text } = Typography
const { TextArea } = Input

const LeadsPage = () => {
  const { user } = useAuthStore()
  const queryClient = useQueryClient()
  const [searchText, setSearchText] = useState('')
  const [statusFilter, setStatusFilter] = useState('')
  const [selectedLead, setSelectedLead] = useState(null)
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [modalType, setModalType] = useState('') // 'edit', 'call', 'assign'
  const [form] = Form.useForm()

  // Fetch leads based on user role
  const { data: leadsData, isLoading } = useQuery(
    ['leads', searchText, statusFilter],
    async () => {
      const params = new URLSearchParams()
      if (searchText) params.append('search', searchText)
      if (statusFilter) params.append('status', statusFilter)
      
      // For call center agents, get only assigned leads
      const endpoint = user?.role === 'call_center' ? '/leads/my/assigned' : '/leads'
      const response = await axios.get(`${endpoint}?${params}`)
      return response.data.data
    },
    {
      refetchInterval: 30000 // Refresh every 30 seconds
    }
  )

  // Update lead mutation
  const updateLeadMutation = useMutation(
    async ({ leadId, data }) => {
      const response = await axios.put(`/leads/${leadId}`, data)
      return response.data
    },
    {
      onSuccess: () => {
        message.success('Lead updated successfully')
        queryClient.invalidateQueries('leads')
        setIsModalVisible(false)
        form.resetFields()
      },
      onError: (error) => {
        message.error(error.response?.data?.message || 'Failed to update lead')
      }
    }
  )

  // Assign lead mutation
  const assignLeadMutation = useMutation(
    async ({ leadId, agentId }) => {
      const response = await axios.post(`/leads/${leadId}/assign`, { agent_id: agentId })
      return response.data
    },
    {
      onSuccess: () => {
        message.success('Lead assigned successfully')
        queryClient.invalidateQueries('leads')
      },
      onError: (error) => {
        message.error(error.response?.data?.message || 'Failed to assign lead')
      }
    }
  )

  const getStatusColor = (status) => {
    const colors = {
      new: 'blue',
      contacted: 'orange',
      interested: 'green',
      not_interested: 'red',
      callback_scheduled: 'purple',
      test_scheduled: 'cyan',
      enrolled: 'success',
      lost: 'default'
    }
    return colors[status] || 'default'
  }

  const getStatusText = (status) => {
    const texts = {
      new: 'New',
      contacted: 'Contacted',
      interested: 'Interested',
      not_interested: 'Not Interested',
      callback_scheduled: 'Callback Scheduled',
      test_scheduled: 'Test Scheduled',
      enrolled: 'Enrolled',
      lost: 'Lost'
    }
    return texts[status] || status
  }

  const handleEditLead = (lead) => {
    setSelectedLead(lead)
    setModalType('edit')
    form.setFieldsValue({
      ...lead,
      follow_up_date: lead.follow_up_date ? dayjs(lead.follow_up_date) : null
    })
    setIsModalVisible(true)
  }

  const handleCallLead = (lead) => {
    setSelectedLead(lead)
    setModalType('call')
    form.setFieldsValue({
      outcome: '',
      notes: '',
      follow_up_required: false,
      follow_up_date: null
    })
    setIsModalVisible(true)
  }

  const handleModalSubmit = async (values) => {
    if (modalType === 'edit') {
      await updateLeadMutation.mutateAsync({
        leadId: selectedLead.id,
        data: {
          ...values,
          follow_up_date: values.follow_up_date ? values.follow_up_date.toISOString() : null
        }
      })
    } else if (modalType === 'call') {
      // Here you would typically log the call and update the lead
      await updateLeadMutation.mutateAsync({
        leadId: selectedLead.id,
        data: {
          status: values.outcome === 'interested' ? 'interested' : 
                 values.outcome === 'not_interested' ? 'not_interested' : 'contacted',
          notes: values.notes,
          follow_up_date: values.follow_up_required && values.follow_up_date ? 
                          values.follow_up_date.toISOString() : null
        }
      })
    }
  }

  const columns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <div>
          <Text strong>{text}</Text>
          <br />
          <Text type="secondary" style={{ fontSize: 12 }}>
            {dayjs(record.created_at).format('MMM DD, YYYY')}
          </Text>
        </div>
      )
    },
    {
      title: 'Phone',
      dataIndex: 'phone',
      key: 'phone',
      render: (phone) => (
        <Space>
          <Text copyable>{phone}</Text>
          <Button
            type="text"
            size="small"
            icon={<PhoneOutlined />}
            href={`tel:${phone}`}
          />
        </Space>
      )
    },
    {
      title: 'Courses',
      dataIndex: 'course_preferences',
      key: 'course_preferences',
      render: (courses) => (
        <div>
          {courses?.map((course, index) => (
            <Tag key={index} style={{ marginBottom: 4 }}>
              {course.replace('_', ' ').replace('-', ' ')}
            </Tag>
          ))}
        </div>
      )
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      )
    },
    {
      title: 'Agent',
      dataIndex: 'agent_first_name',
      key: 'agent',
      render: (firstName, record) => (
        firstName ? `${firstName} ${record.agent_last_name}` : 'Unassigned'
      )
    },
    {
      title: 'Follow Up',
      dataIndex: 'follow_up_date',
      key: 'follow_up_date',
      render: (date) => (
        date ? (
          <Tooltip title={dayjs(date).format('MMMM DD, YYYY HH:mm')}>
            <Tag color={dayjs(date).isBefore(dayjs()) ? 'red' : 'blue'}>
              {dayjs(date).format('MMM DD')}
            </Tag>
          </Tooltip>
        ) : null
      )
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Tooltip title="Call Lead">
            <Button
              type="primary"
              size="small"
              icon={<PhoneOutlined />}
              onClick={() => handleCallLead(record)}
            />
          </Tooltip>
          <Tooltip title="Edit Lead">
            <Button
              size="small"
              icon={<EditOutlined />}
              onClick={() => handleEditLead(record)}
            />
          </Tooltip>
          {user?.role === 'admin' && (
            <Tooltip title="Send SMS">
              <Button
                size="small"
                icon={<MessageOutlined />}
                onClick={() => {
                  // SMS functionality would go here
                  message.info('SMS feature coming soon')
                }}
              />
            </Tooltip>
          )}
        </Space>
      )
    }
  ]

  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>
          {user?.role === 'call_center' ? 'My Leads' : 'Lead Management'}
        </Title>
        <Text type="secondary">
          Manage and track potential students
        </Text>
      </div>

      {/* Statistics Cards */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="Total Leads"
              value={leadsData?.leads?.length || 0}
              prefix={<UserAddOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="New Leads"
              value={leadsData?.leads?.filter(l => l.status === 'new').length || 0}
              prefix={<UserAddOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Interested"
              value={leadsData?.leads?.filter(l => l.status === 'interested').length || 0}
              prefix={<UserAddOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Enrolled"
              value={leadsData?.leads?.filter(l => l.status === 'enrolled').length || 0}
              prefix={<UserAddOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Filters */}
      <Card style={{ marginBottom: 16 }}>
        <Row gutter={16}>
          <Col span={8}>
            <Input
              placeholder="Search by name or phone"
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              allowClear
            />
          </Col>
          <Col span={6}>
            <Select
              placeholder="Filter by status"
              value={statusFilter}
              onChange={setStatusFilter}
              allowClear
              style={{ width: '100%' }}
            >
              <Select.Option value="new">New</Select.Option>
              <Select.Option value="contacted">Contacted</Select.Option>
              <Select.Option value="interested">Interested</Select.Option>
              <Select.Option value="not_interested">Not Interested</Select.Option>
              <Select.Option value="callback_scheduled">Callback Scheduled</Select.Option>
              <Select.Option value="test_scheduled">Test Scheduled</Select.Option>
              <Select.Option value="enrolled">Enrolled</Select.Option>
              <Select.Option value="lost">Lost</Select.Option>
            </Select>
          </Col>
        </Row>
      </Card>

      {/* Leads Table */}
      <Card>
        <Table
          columns={columns}
          dataSource={leadsData?.leads || []}
          loading={isLoading}
          rowKey="id"
          pagination={{
            pageSize: 20,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `Total ${total} leads`
          }}
        />
      </Card>

      {/* Edit/Call Modal */}
      <Modal
        title={modalType === 'edit' ? 'Edit Lead' : 'Log Call'}
        open={isModalVisible}
        onCancel={() => {
          setIsModalVisible(false)
          form.resetFields()
        }}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleModalSubmit}
        >
          {modalType === 'edit' ? (
            <>
              <Form.Item name="name" label="Name" rules={[{ required: true }]}>
                <Input />
              </Form.Item>
              <Form.Item name="phone" label="Phone" rules={[{ required: true }]}>
                <Input />
              </Form.Item>
              <Form.Item name="status" label="Status" rules={[{ required: true }]}>
                <Select>
                  <Select.Option value="new">New</Select.Option>
                  <Select.Option value="contacted">Contacted</Select.Option>
                  <Select.Option value="interested">Interested</Select.Option>
                  <Select.Option value="not_interested">Not Interested</Select.Option>
                  <Select.Option value="callback_scheduled">Callback Scheduled</Select.Option>
                  <Select.Option value="test_scheduled">Test Scheduled</Select.Option>
                  <Select.Option value="enrolled">Enrolled</Select.Option>
                  <Select.Option value="lost">Lost</Select.Option>
                </Select>
              </Form.Item>
              <Form.Item name="notes" label="Notes">
                <TextArea rows={4} />
              </Form.Item>
              <Form.Item name="follow_up_date" label="Follow Up Date">
                <DatePicker showTime style={{ width: '100%' }} />
              </Form.Item>
            </>
          ) : (
            <>
              <Form.Item name="outcome" label="Call Outcome" rules={[{ required: true }]}>
                <Select>
                  <Select.Option value="contacted">Contacted</Select.Option>
                  <Select.Option value="interested">Interested</Select.Option>
                  <Select.Option value="not_interested">Not Interested</Select.Option>
                  <Select.Option value="callback_scheduled">Callback Scheduled</Select.Option>
                </Select>
              </Form.Item>
              <Form.Item name="notes" label="Call Notes">
                <TextArea rows={4} placeholder="Enter call notes..." />
              </Form.Item>
              <Form.Item name="follow_up_required" valuePropName="checked">
                <input type="checkbox" /> Follow-up required
              </Form.Item>
              <Form.Item name="follow_up_date" label="Follow Up Date">
                <DatePicker showTime style={{ width: '100%' }} />
              </Form.Item>
            </>
          )}
          
          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setIsModalVisible(false)}>
                Cancel
              </Button>
              <Button 
                type="primary" 
                htmlType="submit"
                loading={updateLeadMutation.isLoading}
              >
                {modalType === 'edit' ? 'Update Lead' : 'Log Call'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default LeadsPage
