const express = require('express');
const router = express.Router();
const studentController = require('../controllers/studentController');
const { authenticate, requirePermission } = require('../middleware/auth');

// All routes require authentication
router.use(authenticate);

// Get all students with filtering and pagination
router.get('/', requirePermission('students:read'), async (req, res) => {
    try {
        const filters = {
            search: req.query.search,
            level: req.query.level,
            is_active: req.query.is_active,
            group_id: req.query.group_id,
            enrollment_date_from: req.query.enrollment_date_from,
            enrollment_date_to: req.query.enrollment_date_to
        };

        const pagination = {
            page: parseInt(req.query.page) || 1,
            limit: parseInt(req.query.limit) || 20,
            sort_by: req.query.sort_by || 'enrollment_date',
            sort_order: req.query.sort_order || 'DESC'
        };

        const result = await studentController.getStudents(filters, pagination);

        if (result.success) {
            res.json(result);
        } else {
            res.status(500).json(result);
        }
    } catch (error) {
        console.error('Get students error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to retrieve students'
        });
    }
});

// Get student by ID
router.get('/:id', requirePermission('students:read'), async (req, res) => {
    try {
        const result = await studentController.getStudentById(req.params.id);

        if (result.success) {
            res.json(result);
        } else {
            res.status(404).json(result);
        }
    } catch (error) {
        console.error('Get student error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to retrieve student'
        });
    }
});

// Create new student
router.post('/', requirePermission('students:create'), async (req, res) => {
    try {
        const result = await studentController.createStudent(req.body);

        if (result.success) {
            res.status(201).json(result);
        } else {
            res.status(400).json(result);
        }
    } catch (error) {
        console.error('Create student error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to create student'
        });
    }
});

// Update student
router.put('/:id', requirePermission('students:update'), async (req, res) => {
    try {
        const result = await studentController.updateStudent(req.params.id, req.body);

        if (result.success) {
            res.json(result);
        } else {
            res.status(400).json(result);
        }
    } catch (error) {
        console.error('Update student error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to update student'
        });
    }
});

// Enroll student in group
router.post('/:id/enroll', requirePermission('students:update'), async (req, res) => {
    try {
        const { group_id } = req.body;

        if (!group_id) {
            return res.status(400).json({
                success: false,
                message: 'Group ID is required'
            });
        }

        const result = await studentController.enrollStudentInGroup(req.params.id, group_id);

        if (result.success) {
            res.json(result);
        } else {
            res.status(400).json(result);
        }
    } catch (error) {
        console.error('Enroll student error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to enroll student'
        });
    }
});

module.exports = router;
