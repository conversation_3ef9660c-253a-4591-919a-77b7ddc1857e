/*
   Innovative Centre - Jobs Form JavaScript
   Author: Augment Agent
   Version: 1.0
*/

document.addEventListener('DOMContentLoaded', function() {
    console.log('Jobs form script loaded');

    // Initialize the job application form
    initJobApplicationForm();

    // Pre-select position if provided in URL
    preSelectPositionFromURL();

    // Handle file uploads
    initFileUploads();

    // Handle photo upload
    initPhotoUpload();
});

// Initialize the job application form
function initJobApplicationForm() {
    const jobApplicationForm = document.getElementById('job-application-form');

    if (jobApplicationForm) {
        console.log('Job application form found');

        jobApplicationForm.addEventListener('submit', function(e) {
            e.preventDefault();
            console.log('Form submitted');

            // Get form data
            const formData = new FormData(jobApplicationForm);
            const formValues = {};
            for (let [key, value] of formData.entries()) {
                formValues[key] = value;
            }

            // Here you would normally send the data to your server
            // For demo purposes, we'll just show a success message
            jobApplicationForm.innerHTML = `
                <div class="form-success">
                    <i class="fas fa-check-circle"></i>
                    <h3>Application Submitted!</h3>
                    <p>Thank you for your interest in joining our team. We will review your application and contact you soon.</p>
                    <a href="jobs.html" class="btn btn-primary">Back to Jobs</a>
                </div>
            `;

            // Log the form data to console (for demo purposes)
            console.log('Job application submitted:', formValues);

            // Scroll to top of form
            window.scrollTo({
                top: document.querySelector('.application-form-container').offsetTop - 100,
                behavior: 'smooth'
            });
        });
    } else {
        console.error('Job application form not found');
    }
}

// Pre-select position if provided in URL
function preSelectPositionFromURL() {
    const urlParams = new URLSearchParams(window.location.search);
    const position = urlParams.get('position');

    if (position) {
        const positionSelect = document.getElementById('position');

        if (positionSelect) {
            // Find the option with matching value
            for (let i = 0; i < positionSelect.options.length; i++) {
                if (positionSelect.options[i].value === position) {
                    positionSelect.selectedIndex = i;
                    break;
                }
            }
        }
    }
}

// Handle file uploads
function initFileUploads() {
    const fileInputs = document.querySelectorAll('.file-input');

    fileInputs.forEach(input => {
        input.addEventListener('change', function() {
            const fileName = this.files[0] ? this.files[0].name : '';
            const fileNameElement = this.parentElement.querySelector('.file-name');

            if (fileNameElement) {
                fileNameElement.textContent = fileName;
            }
        });
    });
}

// Handle photo upload
function initPhotoUpload() {
    const photoInput = document.getElementById('photo');
    const photoPlaceholder = document.querySelector('.photo-placeholder');

    if (photoInput && photoPlaceholder) {
        photoInput.addEventListener('change', function() {
            if (this.files && this.files[0]) {
                const reader = new FileReader();

                reader.onload = function(e) {
                    // Clear placeholder content
                    photoPlaceholder.innerHTML = '';

                    // Create image element
                    const img = document.createElement('img');
                    img.src = e.target.result;

                    // Add image to placeholder
                    photoPlaceholder.appendChild(img);
                };

                reader.readAsDataURL(this.files[0]);
            }
        });
    }
}
