const express = require('express');
const router = express.Router();
const { query } = require('../config/database');
const { authenticate, authorize } = require('../middleware/auth');

// All dashboard routes require authentication
router.use(authenticate);

// Get dashboard statistics for different user roles
router.get('/stats', async (req, res) => {
    try {
        const userRole = req.user.role;
        let stats = {};

        switch (userRole) {
            case 'admin':
                stats = await getAdminStats();
                break;
            case 'reception':
                stats = await getReceptionStats();
                break;
            case 'call_center':
                stats = await getCallCenterStats(req.user.id);
                break;
            case 'testing_department':
                stats = await getTestingDepartmentStats();
                break;
            case 'cashier':
                stats = await getCashierStats();
                break;
            case 'teacher':
                stats = await getTeacherStats(req.user.id);
                break;
            case 'student':
                stats = await getStudentStats(req.user.id);
                break;
            case 'parent':
                stats = await getParentStats(req.user.id);
                break;
            default:
                return res.status(403).json({
                    success: false,
                    message: 'Access denied'
                });
        }

        res.json({
            success: true,
            data: stats
        });
    } catch (error) {
        console.error('Dashboard stats error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to retrieve dashboard statistics'
        });
    }
});

// Admin dashboard statistics
async function getAdminStats() {
    const [
        totalStudents,
        totalTeachers,
        totalLeads,
        totalGroups,
        recentEnrollments,
        monthlyRevenue,
        leadConversion
    ] = await Promise.all([
        query('SELECT COUNT(*) as count FROM students WHERE is_active = true'),
        query('SELECT COUNT(*) as count FROM teachers WHERE is_active = true'),
        query('SELECT COUNT(*) as count FROM leads WHERE created_at >= CURRENT_DATE - INTERVAL \'30 days\''),
        query('SELECT COUNT(*) as count FROM groups WHERE status = \'active\''),
        query('SELECT COUNT(*) as count FROM students WHERE enrollment_date >= CURRENT_DATE - INTERVAL \'7 days\''),
        query(`SELECT COALESCE(SUM(amount), 0) as total 
               FROM payments 
               WHERE status = 'paid' 
               AND paid_date >= DATE_TRUNC('month', CURRENT_DATE)`),
        query(`SELECT 
                COUNT(CASE WHEN status = 'enrolled' THEN 1 END) as enrolled,
                COUNT(*) as total
               FROM leads 
               WHERE created_at >= CURRENT_DATE - INTERVAL '30 days'`)
    ]);

    const conversionRate = leadConversion.rows[0].total > 0 
        ? (leadConversion.rows[0].enrolled / leadConversion.rows[0].total * 100).toFixed(2)
        : 0;

    return {
        total_students: parseInt(totalStudents.rows[0].count),
        total_teachers: parseInt(totalTeachers.rows[0].count),
        new_leads_30_days: parseInt(totalLeads.rows[0].count),
        active_groups: parseInt(totalGroups.rows[0].count),
        recent_enrollments: parseInt(recentEnrollments.rows[0].count),
        monthly_revenue: parseFloat(monthlyRevenue.rows[0].total),
        lead_conversion_rate: parseFloat(conversionRate)
    };
}

// Reception dashboard statistics
async function getReceptionStats() {
    const [
        todayLeads,
        pendingPayments,
        todayEnrollments,
        upcomingTests
    ] = await Promise.all([
        query('SELECT COUNT(*) as count FROM leads WHERE DATE(created_at) = CURRENT_DATE'),
        query('SELECT COUNT(*) as count FROM payments WHERE status = \'pending\' AND due_date <= CURRENT_DATE + INTERVAL \'7 days\''),
        query('SELECT COUNT(*) as count FROM students WHERE DATE(enrollment_date) = CURRENT_DATE'),
        query(`SELECT COUNT(*) as count FROM test_results tr
               JOIN tests t ON tr.test_id = t.id
               WHERE t.type = 'placement' 
               AND DATE(tr.start_time) = CURRENT_DATE + INTERVAL '1 day'`)
    ]);

    return {
        today_leads: parseInt(todayLeads.rows[0].count),
        pending_payments: parseInt(pendingPayments.rows[0].count),
        today_enrollments: parseInt(todayEnrollments.rows[0].count),
        upcoming_tests: parseInt(upcomingTests.rows[0].count)
    };
}

// Call center agent statistics
async function getCallCenterStats(agentId) {
    const [
        assignedLeads,
        todayCalls,
        conversionRate,
        followUps
    ] = await Promise.all([
        query('SELECT COUNT(*) as count FROM leads WHERE assigned_agent_id = $1 AND status NOT IN (\'enrolled\', \'lost\')', [agentId]),
        query('SELECT COUNT(*) as count FROM call_logs WHERE agent_id = $1 AND DATE(created_at) = CURRENT_DATE', [agentId]),
        query(`SELECT 
                COUNT(CASE WHEN status = 'enrolled' THEN 1 END) as enrolled,
                COUNT(*) as total
               FROM leads 
               WHERE assigned_agent_id = $1 
               AND created_at >= CURRENT_DATE - INTERVAL '30 days'`, [agentId]),
        query('SELECT COUNT(*) as count FROM leads WHERE assigned_agent_id = $1 AND follow_up_date <= CURRENT_DATE', [agentId])
    ]);

    const conversionRatePercent = conversionRate.rows[0].total > 0 
        ? (conversionRate.rows[0].enrolled / conversionRate.rows[0].total * 100).toFixed(2)
        : 0;

    return {
        assigned_leads: parseInt(assignedLeads.rows[0].count),
        today_calls: parseInt(todayCalls.rows[0].count),
        conversion_rate: parseFloat(conversionRatePercent),
        pending_follow_ups: parseInt(followUps.rows[0].count)
    };
}

// Testing department statistics
async function getTestingDepartmentStats() {
    const [
        todayTests,
        pendingResults,
        monthlyTests,
        averageScore
    ] = await Promise.all([
        query('SELECT COUNT(*) as count FROM test_results WHERE DATE(start_time) = CURRENT_DATE'),
        query('SELECT COUNT(*) as count FROM test_results WHERE score IS NULL'),
        query('SELECT COUNT(*) as count FROM test_results WHERE start_time >= DATE_TRUNC(\'month\', CURRENT_DATE)'),
        query('SELECT AVG(percentage) as avg FROM test_results WHERE score IS NOT NULL AND start_time >= CURRENT_DATE - INTERVAL \'30 days\'')
    ]);

    return {
        today_tests: parseInt(todayTests.rows[0].count),
        pending_results: parseInt(pendingResults.rows[0].count),
        monthly_tests: parseInt(monthlyTests.rows[0].count),
        average_score: parseFloat(averageScore.rows[0].avg || 0).toFixed(2)
    };
}

// Cashier statistics
async function getCashierStats() {
    const [
        todayPayments,
        pendingPayments,
        monthlyRevenue,
        overduePayments
    ] = await Promise.all([
        query('SELECT COUNT(*) as count, COALESCE(SUM(amount), 0) as total FROM payments WHERE DATE(paid_date) = CURRENT_DATE'),
        query('SELECT COUNT(*) as count FROM payments WHERE status = \'pending\''),
        query('SELECT COALESCE(SUM(amount), 0) as total FROM payments WHERE status = \'paid\' AND paid_date >= DATE_TRUNC(\'month\', CURRENT_DATE)'),
        query('SELECT COUNT(*) as count FROM payments WHERE status = \'pending\' AND due_date < CURRENT_DATE')
    ]);

    return {
        today_payments_count: parseInt(todayPayments.rows[0].count),
        today_payments_amount: parseFloat(todayPayments.rows[0].total),
        pending_payments: parseInt(pendingPayments.rows[0].count),
        monthly_revenue: parseFloat(monthlyRevenue.rows[0].total),
        overdue_payments: parseInt(overduePayments.rows[0].count)
    };
}

// Teacher statistics
async function getTeacherStats(teacherId) {
    const [
        myGroups,
        totalStudents,
        todayClasses,
        averageAttendance
    ] = await Promise.all([
        query('SELECT COUNT(*) as count FROM groups WHERE teacher_id = (SELECT id FROM teachers WHERE user_id = $1) AND status = \'active\'', [teacherId]),
        query(`SELECT COUNT(DISTINCT ge.student_id) as count 
               FROM group_enrollments ge
               JOIN groups g ON ge.group_id = g.id
               JOIN teachers t ON g.teacher_id = t.id
               WHERE t.user_id = $1 AND ge.status = 'active'`, [teacherId]),
        query(`SELECT COUNT(*) as count FROM groups g
               JOIN teachers t ON g.teacher_id = t.id
               WHERE t.user_id = $1 
               AND g.status = 'active'
               AND g.schedule::text LIKE '%"' || TO_CHAR(CURRENT_DATE, 'Day') || '"%'`, [teacherId]),
        query(`SELECT AVG(attendance_rate) as avg FROM (
                SELECT 
                    COUNT(CASE WHEN a.status = 'present' THEN 1 END) * 100.0 / COUNT(*) as attendance_rate
                FROM attendance a
                JOIN groups g ON a.group_id = g.id
                JOIN teachers t ON g.teacher_id = t.id
                WHERE t.user_id = $1
                AND a.class_date >= CURRENT_DATE - INTERVAL '30 days'
                GROUP BY g.id
               ) as group_attendance`, [teacherId])
    ]);

    return {
        my_groups: parseInt(myGroups.rows[0].count),
        total_students: parseInt(totalStudents.rows[0].count),
        today_classes: parseInt(todayClasses.rows[0].count),
        average_attendance: parseFloat(averageAttendance.rows[0].avg || 0).toFixed(2)
    };
}

// Student statistics
async function getStudentStats(userId) {
    const [
        currentGroups,
        completedTests,
        attendanceRate,
        paymentStatus
    ] = await Promise.all([
        query(`SELECT COUNT(*) as count FROM group_enrollments ge
               JOIN students s ON ge.student_id = s.id
               WHERE s.user_id = $1 AND ge.status = 'active'`, [userId]),
        query(`SELECT COUNT(*) as count FROM test_results tr
               JOIN students s ON tr.student_id = s.id
               WHERE s.user_id = $1`, [userId]),
        query(`SELECT 
                COUNT(CASE WHEN a.status = 'present' THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0) as rate
               FROM attendance a
               JOIN students s ON a.student_id = s.id
               WHERE s.user_id = $1
               AND a.class_date >= CURRENT_DATE - INTERVAL '30 days'`, [userId]),
        query(`SELECT COUNT(*) as pending FROM payments p
               JOIN students s ON p.student_id = s.id
               WHERE s.user_id = $1 AND p.status = 'pending'`, [userId])
    ]);

    return {
        current_groups: parseInt(currentGroups.rows[0].count),
        completed_tests: parseInt(completedTests.rows[0].count),
        attendance_rate: parseFloat(attendanceRate.rows[0].rate || 0).toFixed(2),
        pending_payments: parseInt(paymentStatus.rows[0].pending)
    };
}

// Parent statistics
async function getParentStats(parentId) {
    const [
        children,
        totalGroups,
        averageAttendance,
        pendingPayments
    ] = await Promise.all([
        query('SELECT COUNT(*) as count FROM students WHERE parent_id = $1 AND is_active = true', [parentId]),
        query(`SELECT COUNT(DISTINCT ge.group_id) as count 
               FROM group_enrollments ge
               JOIN students s ON ge.student_id = s.id
               WHERE s.parent_id = $1 AND ge.status = 'active'`, [parentId]),
        query(`SELECT AVG(attendance_rate) as avg FROM (
                SELECT 
                    COUNT(CASE WHEN a.status = 'present' THEN 1 END) * 100.0 / COUNT(*) as attendance_rate
                FROM attendance a
                JOIN students s ON a.student_id = s.id
                WHERE s.parent_id = $1
                AND a.class_date >= CURRENT_DATE - INTERVAL '30 days'
                GROUP BY s.id
               ) as student_attendance`, [parentId]),
        query(`SELECT COUNT(*) as count FROM payments p
               JOIN students s ON p.student_id = s.id
               WHERE s.parent_id = $1 AND p.status = 'pending'`, [parentId])
    ]);

    return {
        children_count: parseInt(children.rows[0].count),
        total_groups: parseInt(totalGroups.rows[0].count),
        average_attendance: parseFloat(averageAttendance.rows[0].avg || 0).toFixed(2),
        pending_payments: parseInt(pendingPayments.rows[0].count)
    };
}

module.exports = router;
