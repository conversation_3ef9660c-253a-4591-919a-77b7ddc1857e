const express = require('express');
const router = express.Router();
const leadController = require('../controllers/leadController');
const { authenticate, authorize, requirePermission } = require('../middleware/auth');

// Public endpoint for landing page form submission
router.post('/submit', async (req, res) => {
    try {
        const result = await leadController.createLead(req.body);
        
        if (result.success) {
            res.status(201).json(result);
        } else {
            res.status(400).json(result);
        }
    } catch (error) {
        console.error('Lead submission error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to submit lead'
        });
    }
});

// Protected routes - require authentication
router.use(authenticate);

// Get all leads with filtering and pagination
router.get('/', requirePermission('leads:read'), async (req, res) => {
    try {
        const filters = {
            status: req.query.status,
            assigned_agent_id: req.query.assigned_agent_id,
            source: req.query.source,
            search: req.query.search,
            date_from: req.query.date_from,
            date_to: req.query.date_to
        };

        const pagination = {
            page: parseInt(req.query.page) || 1,
            limit: parseInt(req.query.limit) || 20,
            sort_by: req.query.sort_by || 'created_at',
            sort_order: req.query.sort_order || 'DESC'
        };

        const result = await leadController.getLeads(filters, pagination);
        
        if (result.success) {
            res.json(result);
        } else {
            res.status(500).json(result);
        }
    } catch (error) {
        console.error('Get leads error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to retrieve leads'
        });
    }
});

// Get lead statistics
router.get('/stats', requirePermission('leads:read'), async (req, res) => {
    try {
        const filters = {
            date_from: req.query.date_from,
            date_to: req.query.date_to,
            agent_id: req.query.agent_id
        };

        const result = await leadController.getLeadStats(filters);
        
        if (result.success) {
            res.json(result);
        } else {
            res.status(500).json(result);
        }
    } catch (error) {
        console.error('Get lead stats error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to retrieve lead statistics'
        });
    }
});

// Get lead by ID
router.get('/:id', requirePermission('leads:read'), async (req, res) => {
    try {
        const result = await leadController.getLeadById(req.params.id);
        
        if (result.success) {
            res.json(result);
        } else {
            res.status(404).json(result);
        }
    } catch (error) {
        console.error('Get lead by ID error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to retrieve lead'
        });
    }
});

// Create new lead (for internal use)
router.post('/', requirePermission('leads:create'), async (req, res) => {
    try {
        const result = await leadController.createLead(req.body);
        
        if (result.success) {
            res.status(201).json(result);
        } else {
            res.status(400).json(result);
        }
    } catch (error) {
        console.error('Create lead error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to create lead'
        });
    }
});

// Update lead
router.put('/:id', requirePermission('leads:update'), async (req, res) => {
    try {
        const result = await leadController.updateLead(req.params.id, req.body);
        
        if (result.success) {
            res.json(result);
        } else {
            res.status(400).json(result);
        }
    } catch (error) {
        console.error('Update lead error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to update lead'
        });
    }
});

// Assign lead to agent
router.post('/:id/assign', requirePermission('leads:update'), async (req, res) => {
    try {
        const { agent_id } = req.body;
        
        if (!agent_id) {
            return res.status(400).json({
                success: false,
                message: 'Agent ID is required'
            });
        }

        const result = await leadController.assignLeadToAgent(req.params.id, agent_id);
        
        if (result.success) {
            res.json(result);
        } else {
            res.status(400).json(result);
        }
    } catch (error) {
        console.error('Assign lead error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to assign lead'
        });
    }
});

// Get leads assigned to current user (for call center agents)
router.get('/my/assigned', authenticate, async (req, res) => {
    try {
        // Only call center agents and reception can access their assigned leads
        if (!['call_center', 'reception', 'admin'].includes(req.user.role)) {
            return res.status(403).json({
                success: false,
                message: 'Access denied'
            });
        }

        const filters = {
            assigned_agent_id: req.user.id,
            status: req.query.status,
            search: req.query.search
        };

        const pagination = {
            page: parseInt(req.query.page) || 1,
            limit: parseInt(req.query.limit) || 20,
            sort_by: req.query.sort_by || 'created_at',
            sort_order: req.query.sort_order || 'DESC'
        };

        const result = await leadController.getLeads(filters, pagination);
        
        if (result.success) {
            res.json(result);
        } else {
            res.status(500).json(result);
        }
    } catch (error) {
        console.error('Get my assigned leads error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to retrieve assigned leads'
        });
    }
});

// Bulk update lead status
router.post('/bulk/update-status', requirePermission('leads:update'), async (req, res) => {
    try {
        const { lead_ids, status } = req.body;
        
        if (!lead_ids || !Array.isArray(lead_ids) || lead_ids.length === 0) {
            return res.status(400).json({
                success: false,
                message: 'Lead IDs array is required'
            });
        }

        if (!status) {
            return res.status(400).json({
                success: false,
                message: 'Status is required'
            });
        }

        const validStatuses = ['new', 'contacted', 'interested', 'not_interested', 'callback_scheduled', 'test_scheduled', 'enrolled', 'lost'];
        if (!validStatuses.includes(status)) {
            return res.status(400).json({
                success: false,
                message: 'Invalid status'
            });
        }

        // Update multiple leads
        const updatePromises = lead_ids.map(leadId => 
            leadController.updateLead(leadId, { status })
        );

        const results = await Promise.all(updatePromises);
        const successCount = results.filter(r => r.success).length;
        const failureCount = results.length - successCount;

        res.json({
            success: true,
            message: `Updated ${successCount} leads successfully${failureCount > 0 ? `, ${failureCount} failed` : ''}`,
            data: {
                total: results.length,
                successful: successCount,
                failed: failureCount
            }
        });
    } catch (error) {
        console.error('Bulk update leads error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to update leads'
        });
    }
});

module.exports = router;
