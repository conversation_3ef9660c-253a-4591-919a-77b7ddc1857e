const jwt = require('jsonwebtoken');
const { query } = require('../config/database');

// JWT secret key
const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key';

// Generate JWT token
const generateToken = (payload) => {
    return jwt.sign(payload, JWT_SECRET, {
        expiresIn: process.env.JWT_EXPIRES_IN || '7d'
    });
};

// Verify JWT token
const verifyToken = (token) => {
    try {
        return jwt.verify(token, JWT_SECRET);
    } catch (error) {
        return null;
    }
};

// Authentication middleware
const authenticate = async (req, res, next) => {
    try {
        const authHeader = req.headers.authorization;
        
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return res.status(401).json({
                success: false,
                message: 'Access token required'
            });
        }

        const token = authHeader.substring(7); // Remove 'Bearer ' prefix
        const decoded = verifyToken(token);

        if (!decoded) {
            return res.status(401).json({
                success: false,
                message: 'Invalid or expired token'
            });
        }

        // Get user from database
        const userResult = await query(
            'SELECT id, email, phone, role, first_name, last_name, is_active FROM users WHERE id = $1',
            [decoded.userId]
        );

        if (userResult.rows.length === 0) {
            return res.status(401).json({
                success: false,
                message: 'User not found'
            });
        }

        const user = userResult.rows[0];

        if (!user.is_active) {
            return res.status(401).json({
                success: false,
                message: 'Account is deactivated'
            });
        }

        // Add user to request object
        req.user = user;
        next();
    } catch (error) {
        console.error('Authentication error:', error);
        res.status(500).json({
            success: false,
            message: 'Authentication failed'
        });
    }
};

// Role-based authorization middleware
const authorize = (...allowedRoles) => {
    return (req, res, next) => {
        if (!req.user) {
            return res.status(401).json({
                success: false,
                message: 'Authentication required'
            });
        }

        if (!allowedRoles.includes(req.user.role)) {
            return res.status(403).json({
                success: false,
                message: 'Insufficient permissions'
            });
        }

        next();
    };
};

// Optional authentication middleware (for public endpoints that can benefit from user context)
const optionalAuth = async (req, res, next) => {
    try {
        const authHeader = req.headers.authorization;
        
        if (authHeader && authHeader.startsWith('Bearer ')) {
            const token = authHeader.substring(7);
            const decoded = verifyToken(token);

            if (decoded) {
                const userResult = await query(
                    'SELECT id, email, phone, role, first_name, last_name, is_active FROM users WHERE id = $1',
                    [decoded.userId]
                );

                if (userResult.rows.length > 0 && userResult.rows[0].is_active) {
                    req.user = userResult.rows[0];
                }
            }
        }

        next();
    } catch (error) {
        // Continue without authentication if there's an error
        next();
    }
};

// Check if user has specific permission
const hasPermission = (userRole, requiredPermissions) => {
    const rolePermissions = {
        admin: ['*'], // Admin has all permissions
        reception: [
            'leads:read', 'leads:create', 'leads:update',
            'students:read', 'students:create', 'students:update',
            'groups:read', 'groups:update',
            'payments:read', 'payments:create'
        ],
        testing_department: [
            'tests:read', 'tests:create', 'tests:update',
            'test_results:read', 'test_results:create', 'test_results:update',
            'students:read', 'students:update'
        ],
        call_center: [
            'leads:read', 'leads:update',
            'calls:read', 'calls:create', 'calls:update',
            'students:read'
        ],
        cashier: [
            'payments:read', 'payments:create', 'payments:update',
            'students:read'
        ],
        teacher: [
            'groups:read',
            'students:read', 'students:update',
            'attendance:read', 'attendance:create', 'attendance:update',
            'tests:read', 'test_results:read', 'test_results:create'
        ],
        student: [
            'profile:read', 'profile:update',
            'tests:read', 'test_results:read',
            'payments:read', 'attendance:read'
        ],
        parent: [
            'child:read',
            'child_tests:read', 'child_payments:read', 'child_attendance:read'
        ]
    };

    const userPermissions = rolePermissions[userRole] || [];
    
    // Admin has all permissions
    if (userPermissions.includes('*')) {
        return true;
    }

    // Check if user has any of the required permissions
    return requiredPermissions.some(permission => 
        userPermissions.includes(permission)
    );
};

// Permission-based authorization middleware
const requirePermission = (...requiredPermissions) => {
    return (req, res, next) => {
        if (!req.user) {
            return res.status(401).json({
                success: false,
                message: 'Authentication required'
            });
        }

        if (!hasPermission(req.user.role, requiredPermissions)) {
            return res.status(403).json({
                success: false,
                message: 'Insufficient permissions'
            });
        }

        next();
    };
};

module.exports = {
    generateToken,
    verifyToken,
    authenticate,
    authorize,
    optionalAuth,
    hasPermission,
    requirePermission
};
