/*
   Innovative Centre - Jobs Form Stylesheet
   Author: Augment Agent
   Version: 1.0
*/

/* Application Form Section */
.application-form-section {
    padding: 120px 0 80px;
    background-color: #f8f9fa;
    min-height: 100vh;
    display: block;
    width: 100%;
}

.application-form-container {
    max-width: 800px;
    margin: 0 auto;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    padding: 40px;
    display: block;
}

.application-form-container h2 {
    font-size: 32px;
    font-weight: 700;
    text-align: center;
    margin-bottom: 10px;
    color: var(--primary-color);
}

.form-subtitle {
    text-align: center;
    margin-bottom: 30px;
    color: var(--text-light);
}

/* Form Sections */
.form-section {
    margin-bottom: 40px;
    padding-bottom: 30px;
    border-bottom: 1px solid var(--border-color);
}

.form-section:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.form-section h3 {
    font-size: 22px;
    font-weight: 600;
    margin-bottom: 20px;
    color: var(--text-color);
}

/* Form Groups */
.form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 20px;
    flex: 1;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--text-color);
}

.form-group input[type="text"],
.form-group input[type="tel"],
.form-group input[type="date"],
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    font-family: 'Poppins', sans-serif;
    font-size: 16px;
    transition: var(--transition);
}

.form-group input[type="text"]:focus,
.form-group input[type="tel"]:focus,
.form-group input[type="date"]:focus,
.form-group select:focus,
.form-group textarea:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 3px rgba(0, 86, 179, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

/* Radio Group */
.radio-group {
    display: flex;
    gap: 20px;
}

.radio-option {
    display: flex;
    align-items: center;
}

.radio-option input[type="radio"] {
    margin-right: 8px;
    width: 18px;
    height: 18px;
    accent-color: var(--primary-color);
}

/* Photo Upload */
.photo-upload {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 30px;
}

.photo-placeholder {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background-color: #f0f0f0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-bottom: 15px;
    overflow: hidden;
}

.photo-placeholder i {
    font-size: 40px;
    color: #ccc;
    margin-bottom: 5px;
}

.photo-placeholder span {
    font-size: 14px;
    color: #999;
}

.photo-placeholder img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.photo-upload input[type="file"] {
    display: none;
}

.photo-upload-btn {
    background-color: var(--primary-color);
    color: white;
    padding: 8px 15px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    transition: var(--transition);
}

.photo-upload-btn:hover {
    background-color: var(--secondary-color);
}

/* File Upload */
.file-upload-container {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.file-upload {
    display: flex;
    align-items: center;
    gap: 15px;
}

.file-upload input[type="file"] {
    display: none;
}

.file-upload-btn {
    background-color: var(--primary-color);
    color: white;
    padding: 10px 15px;
    border-radius: 5px;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: var(--transition);
}

.file-upload-btn:hover {
    background-color: var(--secondary-color);
}

.file-name {
    font-size: 14px;
    color: var(--text-light);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 200px;
}

.documents-note {
    font-size: 14px;
    color: var(--text-light);
    margin-bottom: 20px;
}

/* Form Actions */
.form-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 30px;
}

.form-actions .btn {
    padding: 12px 25px;
}

/* Form Success */
.form-success {
    text-align: center;
    padding: 30px 0;
}

.form-success i {
    font-size: 60px;
    color: #4CAF50;
    margin-bottom: 20px;
}

.form-success h3 {
    font-size: 24px;
    margin-bottom: 15px;
    color: var(--primary-color);
}

.form-success p {
    margin-bottom: 25px;
    color: var(--text-light);
}

.form-success .btn {
    display: inline-block;
    margin-top: 10px;
}

/* Button Styles */
.btn {
    display: inline-block;
    padding: 12px 25px;
    font-size: 16px;
    font-weight: 500;
    text-align: center;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: #0056b3;
    color: white;
}

.btn-primary:hover {
    background-color: #003d80;
}

.btn-outline {
    background-color: transparent;
    color: #0056b3;
    border: 1px solid #0056b3;
}

.btn-outline:hover {
    background-color: rgba(0, 86, 179, 0.1);
}

/* Responsive Styles */
@media (max-width: 767.98px) {
    .application-form-section {
        padding: 100px 0 60px;
    }

    .application-form-container {
        padding: 30px 20px;
    }

    .application-form-container h2 {
        font-size: 26px;
    }

    .form-section h3 {
        font-size: 20px;
    }

    .form-row {
        flex-direction: column;
        gap: 0;
    }

    .photo-placeholder {
        width: 100px;
        height: 100px;
    }

    .photo-placeholder i {
        font-size: 30px;
    }

    .file-upload {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .file-name {
        max-width: 100%;
    }

    .form-actions {
        flex-direction: column;
        gap: 15px;
    }

    .form-actions .btn {
        width: 100%;
    }

    .radio-group {
        flex-direction: column;
        gap: 10px;
    }
}

/* Extra small devices (portrait phones, less than 576px) */
@media (max-width: 575.98px) {
    .application-form-section {
        padding: 80px 0 40px;
    }

    .application-form-container {
        padding: 20px 15px;
        border-radius: 8px;
    }

    .application-form-container h2 {
        font-size: 22px;
    }

    .form-subtitle {
        font-size: 14px;
    }

    .form-section {
        margin-bottom: 30px;
        padding-bottom: 20px;
    }

    .form-section h3 {
        font-size: 18px;
        margin-bottom: 15px;
    }

    .form-group {
        margin-bottom: 15px;
    }

    .form-group label {
        margin-bottom: 5px;
        font-size: 14px;
    }

    .form-group input[type="text"],
    .form-group input[type="tel"],
    .form-group input[type="date"],
    .form-group select,
    .form-group textarea {
        padding: 10px 12px;
        font-size: 16px; /* Prevents zoom on iOS */
    }

    .form-success i {
        font-size: 50px;
    }

    .form-success h3 {
        font-size: 20px;
    }
}
